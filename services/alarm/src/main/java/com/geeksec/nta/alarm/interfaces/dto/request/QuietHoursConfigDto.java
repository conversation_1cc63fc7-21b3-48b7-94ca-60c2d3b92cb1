package com.geeksec.nta.alarm.interfaces.dto.subscription;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;

/**
 * 免打扰时间配置 DTO
 * 用于配置告警通知的免打扰时间段
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuietHoursConfigDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间（HH:mm 格式）
     */
    private String startTime;

    /**
     * 结束时间（HH:mm 格式）
     */
    private String endTime;

    /**
     * 生效的星期几
     * 1-7 表示周一到周日，null 或空表示每天生效
     */
    private List<Integer> daysOfWeek;

    /**
     * 时区
     */
    @Builder.Default
    private String timezone = "Asia/Shanghai";

    /**
     * 是否允许紧急告警突破免打扰
     */
    @Builder.Default
    private Boolean allowEmergencyBreakthrough = true;

    /**
     * 紧急告警的威胁等级阈值
     * 只有达到此等级的告警才能突破免打扰
     */
    private String emergencyThreatLevel;

    /**
     * 免打扰期间的处理策略
     */
    @Builder.Default
    private String quietStrategy = "DELAY";

    /**
     * 延迟发送的时间（分钟）
     * 当策略为 DELAY 时使用
     */
    private Integer delayMinutes;

    /**
     * 是否在免打扰结束后发送摘要
     */
    @Builder.Default
    private Boolean sendSummaryAfterQuiet = true;

    /**
     * 免打扰策略枚举
     */
    public enum QuietStrategy {
        /** 丢弃通知 */
        DISCARD,
        /** 延迟发送 */
        DELAY,
        /** 免打扰结束后批量发送 */
        BATCH_AFTER,
        /** 免打扰结束后发送摘要 */
        SUMMARY_AFTER
    }

    /**
     * 验证配置是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        // 验证时间格式
        if (startTime == null || !isValidTimeFormat(startTime)) {
            return false;
        }
        if (endTime == null || !isValidTimeFormat(endTime)) {
            return false;
        }
        
        // 验证星期几
        if (daysOfWeek != null) {
            for (Integer day : daysOfWeek) {
                if (day < 1 || day > 7) {
                    return false;
                }
            }
        }
        
        // 验证策略
        if (quietStrategy != null) {
            try {
                QuietStrategy.valueOf(quietStrategy.toUpperCase());
            } catch (IllegalArgumentException e) {
                return false;
            }
        }
        
        // 验证延迟时间
        if ("DELAY".equals(quietStrategy) && (delayMinutes == null || delayMinutes <= 0)) {
            return false;
        }
        
        return true;
    }

    /**
     * 验证时间格式是否正确（HH:mm）
     *
     * @param timeStr 时间字符串
     * @return 是否有效
     */
    private boolean isValidTimeFormat(String timeStr) {
        try {
            LocalTime.parse(timeStr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取开始时间的 LocalTime
     *
     * @return LocalTime
     */
    public LocalTime getStartLocalTime() {
        return startTime != null ? LocalTime.parse(startTime) : null;
    }

    /**
     * 获取结束时间的 LocalTime
     *
     * @return LocalTime
     */
    public LocalTime getEndLocalTime() {
        return endTime != null ? LocalTime.parse(endTime) : null;
    }

    /**
     * 判断是否跨天
     * 例如：22:00 - 06:00
     *
     * @return 是否跨天
     */
    public boolean isCrossDay() {
        LocalTime start = getStartLocalTime();
        LocalTime end = getEndLocalTime();
        return start != null && end != null && start.isAfter(end);
    }

    /**
     * 判断指定时间是否在免打扰时间段内
     *
     * @param time 要检查的时间
     * @return 是否在免打扰时间段
     */
    public boolean isInQuietHours(LocalTime time) {
        LocalTime start = getStartLocalTime();
        LocalTime end = getEndLocalTime();
        
        if (start == null || end == null || time == null) {
            return false;
        }
        
        if (isCrossDay()) {
            // 跨天情况：时间在开始时间之后或结束时间之前
            return time.isAfter(start) || time.isBefore(end) || time.equals(start);
        } else {
            // 不跨天情况：时间在开始和结束时间之间
            return (time.isAfter(start) || time.equals(start)) && time.isBefore(end);
        }
    }

    /**
     * 判断指定星期几是否生效
     *
     * @param dayOfWeek 星期几（1-7）
     * @return 是否生效
     */
    public boolean isEffectiveOnDay(int dayOfWeek) {
        if (daysOfWeek == null || daysOfWeek.isEmpty()) {
            return true; // 没有指定则每天都生效
        }
        return daysOfWeek.contains(dayOfWeek);
    }

    /**
     * 获取免打扰策略枚举
     *
     * @return 策略枚举
     */
    public QuietStrategy getQuietStrategyEnum() {
        try {
            return QuietStrategy.valueOf(quietStrategy.toUpperCase());
        } catch (Exception e) {
            return QuietStrategy.DELAY; // 默认策略
        }
    }

    /**
     * 获取默认的夜间免打扰配置
     *
     * @return 夜间免打扰配置
     */
    public static QuietHoursConfigDto nightQuietConfig() {
        return QuietHoursConfigDto.builder()
                .startTime("22:00")
                .endTime("08:00")
                .allowEmergencyBreakthrough(true)
                .emergencyThreatLevel("HIGH")
                .quietStrategy("SUMMARY_AFTER")
                .sendSummaryAfterQuiet(true)
                .build();
    }

    /**
     * 获取默认的工作时间免打扰配置
     *
     * @return 工作时间免打扰配置
     */
    public static QuietHoursConfigDto workHoursQuietConfig() {
        return QuietHoursConfigDto.builder()
                .startTime("12:00")
                .endTime("14:00")
                .daysOfWeek(List.of(1, 2, 3, 4, 5)) // 工作日
                .allowEmergencyBreakthrough(true)
                .emergencyThreatLevel("CRITICAL")
                .quietStrategy("DELAY")
                .delayMinutes(30)
                .build();
    }
}