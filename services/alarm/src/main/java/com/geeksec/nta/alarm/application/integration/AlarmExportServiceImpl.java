package com.geeksec.nta.alarm.application.integration;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.nta.alarm.application.query.AlarmExportQuery;
import com.geeksec.nta.alarm.application.integration.AlarmExportService;
import com.geeksec.nta.alarm.infrastructure.client.SessionClient;
import com.geeksec.nta.alarm.infrastructure.client.TaskClient;
import com.geeksec.nta.alarm.interfaces.dto.response.ExportTaskResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 告警导出服务实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlarmExportServiceImpl implements AlarmExportService {

    private final SessionClient sessionClient;
    private final TaskClient taskClient;

    @Override
    public ExportTaskResponse createExportTask(AlarmExportQuery query) {
        log.info("创建告警导出任务: {}", query);
        
        // TODO: 实现告警数据导出逻辑
        throw new UnsupportedOperationException("告警数据导出功能待实现");
    }

    @Override
    public ExportTaskResponse getExportTaskStatus(String taskId) {
        log.debug("查询导出任务状态: {}", taskId);
        
        // TODO: 实现导出任务状态查询
        throw new UnsupportedOperationException("导出任务状态查询功能待实现");
    }

    @Override
    public List<ExportTaskResponse> getExportTasks(String userId, int page, int size) {
        log.debug("查询用户导出任务: userId={}, page={}, size={}", userId, page, size);
        
        // TODO: 实现用户导出任务列表查询
        throw new UnsupportedOperationException("用户导出任务列表查询功能待实现");
    }

    @Override
    public boolean cancelExportTask(String taskId, String userId) {
        log.info("取消导出任务: taskId={}, userId={}", taskId, userId);
        
        // TODO: 实现导出任务取消
        throw new UnsupportedOperationException("导出任务取消功能待实现");
    }

    @Override
    public boolean deleteExportTask(String taskId, String userId) {
        log.info("删除导出任务: taskId={}, userId={}", taskId, userId);
        
        // TODO: 实现导出任务删除
        throw new UnsupportedOperationException("导出任务删除功能待实现");
    }

    @Override
    public PcapDownloadResponse prepareAlarmSessionPcap(String userId, List<String> sessionIds,
                                                       String alarmType, Long alarmTime) {
        log.info("准备告警会话PCAP下载: userId={}, sessionCount={}, alarmType={}",
                userId, sessionIds.size(), alarmType);

        try {
            // 统一调用Session模块的下载接口
            // Session模块内部会根据复杂度自动选择同步或异步处理
            SessionClient.PcapDownloadRequest request = new SessionClient.PcapDownloadRequest(
                sessionIds,
                String.format("alarm_%s_%d", alarmType, alarmTime),
                "ALARM", // 请求来源
                userId,
                java.util.Map.of(
                    "alarmType", alarmType,
                    "alarmTime", alarmTime,
                    "source", "alarm_module"
                )
            );

            ApiResponse<SessionClient.PcapDownloadResponse> response =
                sessionClient.downloadSessionsPcap(request);

            if (!response.isSuccess()) {
                return new PcapDownloadResponse(
                    null, "FAILED", 0, 0L, null,
                    "Session下载失败: " + response.getMessage()
                );
            }

            SessionClient.PcapDownloadResponse sessionResponse = response.getData();

            // 转换为Alarm模块的响应格式
            return new PcapDownloadResponse(
                sessionResponse.downloadId(),
                sessionResponse.status(),
                sessionResponse.totalSessions(),
                sessionResponse.estimatedSize(),
                sessionResponse.downloadUrl(),
                sessionResponse.message()
            );

        } catch (Exception e) {
            log.error("准备告警会话PCAP下载失败", e);
            return new PcapDownloadResponse(
                null, "FAILED", 0, 0L, null,
                "系统错误: " + e.getMessage()
            );
        }
    }



    @Override
    public PcapDownloadTaskStatus getPcapDownloadTaskStatus(Integer taskId) {
        log.debug("查询PCAP下载任务状态: {}", taskId);

        try {
            ApiResponse<TaskClient.PcapDownloadTaskResult> response = 
                taskClient.getDownloadTaskStatus(taskId);

            if (!response.isSuccess()) {
                throw new RuntimeException("查询任务状态失败: " + response.getMessage());
            }

            TaskClient.PcapDownloadTaskResult result = response.getData();
            return new PcapDownloadTaskStatus(
                result.taskId(),
                result.status(),
                result.progress(),
                result.totalFileCount(),
                result.processedFileCount(),
                result.archiveFileSize(),
                result.downloadUrl(),
                result.errorMessage(),
                result.createTime(),
                result.completeTime()
            );

        } catch (Exception e) {
            log.error("查询PCAP下载任务状态失败: taskId={}", taskId, e);
            throw new RuntimeException("查询任务状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean cancelPcapDownloadTask(Integer taskId, String userId) {
        log.info("取消PCAP下载任务: taskId={}, userId={}", taskId, userId);

        try {
            ApiResponse<Boolean> response = taskClient.cancelDownloadTask(taskId, userId);
            return response.isSuccess() && Boolean.TRUE.equals(response.getData());
        } catch (Exception e) {
            log.error("取消PCAP下载任务失败: taskId={}, userId={}", taskId, userId, e);
            return false;
        }
    }

    @Override
    public List<PcapDownloadTaskStatus> getUserPcapDownloadTasks(String userId, int page, int size) {
        log.debug("查询用户PCAP下载任务: userId={}, page={}, size={}", userId, page, size);

        try {
            ApiResponse<List<TaskClient.PcapDownloadTaskResult>> response = 
                taskClient.getUserDownloadTasks(userId, page, size);

            if (!response.isSuccess()) {
                throw new RuntimeException("查询用户任务失败: " + response.getMessage());
            }

            return response.getData().stream()
                    .map(result -> new PcapDownloadTaskStatus(
                        result.taskId(),
                        result.status(),
                        result.progress(),
                        result.totalFileCount(),
                        result.processedFileCount(),
                        result.archiveFileSize(),
                        result.downloadUrl(),
                        result.errorMessage(),
                        result.createTime(),
                        result.completeTime()
                    ))
                    .toList();

        } catch (Exception e) {
            log.error("查询用户PCAP下载任务失败: userId={}", userId, e);
            throw new RuntimeException("查询用户任务失败: " + e.getMessage(), e);
        }
    }
}
