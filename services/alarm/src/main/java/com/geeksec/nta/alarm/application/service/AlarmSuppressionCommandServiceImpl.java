package com.geeksec.nta.alarm.application.service;

import com.geeksec.nta.alarm.application.command.CreateSuppressionCommand;
import com.geeksec.nta.alarm.application.command.DeleteSuppressionCommand;
import com.geeksec.nta.alarm.application.command.UpdateSuppressionCommand;
import com.geeksec.nta.alarm.application.service.AlarmSuppressionCommandService;
import com.geeksec.nta.alarm.domain.aggregate.suppression.AlarmSuppression;
import com.geeksec.nta.alarm.domain.repository.AlarmSuppressionRepository;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.List;
import java.util.Optional;

/**
 * 告警抑制命令应用服务实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class AlarmSuppressionCommandServiceImpl implements AlarmSuppressionCommandService {
    
    private final AlarmSuppressionRepository suppressionRepository;
    
    @Override
    public SuppressionId createSuppression(CreateSuppressionCommand command) {
        log.info("创建抑制规则: {}", command);
        
        // 验证命令
        command.validate();
        
        try {
            // 检查名称是否已存在
            if (suppressionRepository.existsByName(command.getSuppressionName())) {
                throw new IllegalArgumentException("抑制规则名称已存在: " + command.getSuppressionName());
            }
            
            // 创建聚合根
            AlarmSuppression suppression = AlarmSuppression.create(
                command.getSuppressionName(),
                command.getVictim(),
                command.getAttacker(),
                command.getLabel(),
                command.getDescription(),
                command.getCreator(),
                command.getExpiryTime(),
                command.getNote()
            );
            
            // 保存
            AlarmSuppression saved = suppressionRepository.save(suppression);
            
            log.info("抑制规则创建成功: id={}", saved.getSuppressionId());
            return saved.getSuppressionId();
            
        } catch (Exception e) {
            log.error("创建抑制规则失败: {}", command, e);
            throw new RuntimeException("创建抑制规则失败", e);
        }
    }
    
    @Override
    public boolean updateSuppression(UpdateSuppressionCommand command) {
        log.info("更新抑制规则: {}", command);
        
        // 验证命令
        command.validate();
        
        try {
            // 查找聚合根
            Optional<AlarmSuppression> suppressionOpt = suppressionRepository.findById(command.getSuppressionId());
            if (suppressionOpt.isEmpty()) {
                throw new IllegalArgumentException("抑制规则不存在: " + command.getSuppressionId());
            }
            
            AlarmSuppression suppression = suppressionOpt.get();
            
            // 检查名称冲突（如果更新了名称）
            if (command.getSuppressionName() != null && 
                !command.getSuppressionName().equals(suppression.getSuppressionName()) &&
                suppressionRepository.existsByNameExcludeId(command.getSuppressionName(), command.getSuppressionId())) {
                throw new IllegalArgumentException("抑制规则名称已存在: " + command.getSuppressionName());
            }
            
            // 更新聚合根
            suppression.update(
                command.getSuppressionName(),
                command.getVictim(),
                command.getAttacker(),
                command.getLabel(),
                command.getDescription(),
                command.getOperator(),
                command.getExpiryTime(),
                command.getNote()
            );
            
            // 处理启用/禁用
            if (command.getEnabled() != null) {
                if (command.getEnabled()) {
                    suppression.enable(command.getOperator());
                } else {
                    suppression.disable(command.getOperator());
                }
            }
            
            // 保存
            suppressionRepository.save(suppression);
            
            log.info("抑制规则更新成功: id={}", command.getSuppressionId());
            return true;
            
        } catch (Exception e) {
            log.error("更新抑制规则失败: {}", command, e);
            throw new RuntimeException("更新抑制规则失败", e);
        }
    }
    
    @Override
    public boolean deleteSuppression(DeleteSuppressionCommand command) {
        log.info("删除抑制规则: {}", command);
        
        // 验证命令
        command.validate();
        
        try {
            // 查找聚合根
            Optional<AlarmSuppression> suppressionOpt = suppressionRepository.findById(command.getSuppressionId());
            if (suppressionOpt.isEmpty()) {
                log.warn("抑制规则不存在: {}", command.getSuppressionId());
                return false;
            }
            
            AlarmSuppression suppression = suppressionOpt.get();
            
            // 调用聚合根的删除方法（发布事件）
            suppression.delete(command.getOperator(), command.getReason());
            
            // 删除
            boolean deleted = suppressionRepository.deleteById(command.getSuppressionId());
            
            if (deleted) {
                log.info("抑制规则删除成功: id={}", command.getSuppressionId());
            } else {
                log.warn("抑制规则删除失败: id={}", command.getSuppressionId());
            }
            
            return deleted;
            
        } catch (Exception e) {
            log.error("删除抑制规则失败: {}", command, e);
            throw new RuntimeException("删除抑制规则失败", e);
        }
    }
    
    @Override
    public BatchCreateResult batchCreateSuppressions(List<CreateSuppressionCommand> commands) {
        log.info("批量创建抑制规则: 数量={}", commands.size());
        
        int successCount = 0;
        int failureCount = 0;
        
        for (CreateSuppressionCommand command : commands) {
            try {
                createSuppression(command);
                successCount++;
            } catch (Exception e) {
                log.error("批量创建抑制规则失败: {}", command, e);
                failureCount++;
            }
        }
        
        log.info("批量创建抑制规则完成: 成功={}, 失败={}", successCount, failureCount);
        return new BatchCreateResult(successCount, failureCount);
    }
    
    @Override
    public int batchDeleteSuppressions(List<DeleteSuppressionCommand> commands) {
        log.info("批量删除抑制规则: 数量={}", commands.size());
        
        int deletedCount = 0;
        
        for (DeleteSuppressionCommand command : commands) {
            try {
                if (deleteSuppression(command)) {
                    deletedCount++;
                }
            } catch (Exception e) {
                log.error("批量删除抑制规则失败: {}", command, e);
            }
        }
        
        log.info("批量删除抑制规则完成: 删除数量={}", deletedCount);
        return deletedCount;
    }
    
    @Override
    public int deleteSuppressionsByCondition(String victim, String attacker, String label, String operator) {
        log.info("根据条件删除抑制规则: victim={}, attacker={}, label={}, operator={}", 
                victim, attacker, label, operator);
        
        try {
            // 查找匹配的抑制规则
            List<AlarmSuppression> suppressions = suppressionRepository.findByCondition(victim, attacker, label);
            
            int deletedCount = 0;
            for (AlarmSuppression suppression : suppressions) {
                try {
                    suppression.delete(operator, "批量删除");
                    if (suppressionRepository.deleteById(suppression.getSuppressionId())) {
                        deletedCount++;
                    }
                } catch (Exception e) {
                    log.error("删除抑制规则失败: id={}", suppression.getSuppressionId(), e);
                }
            }
            
            log.info("根据条件删除抑制规则完成: 删除数量={}", deletedCount);
            return deletedCount;
            
        } catch (Exception e) {
            log.error("根据条件删除抑制规则失败", e);
            throw new RuntimeException("根据条件删除抑制规则失败", e);
        }
    }
    
    @Override
    public boolean enableSuppression(SuppressionId suppressionId, String operator) {
        log.info("启用抑制规则: id={}, operator={}", suppressionId, operator);
        
        try {
            Optional<AlarmSuppression> suppressionOpt = suppressionRepository.findById(suppressionId);
            if (suppressionOpt.isEmpty()) {
                throw new IllegalArgumentException("抑制规则不存在: " + suppressionId);
            }
            
            AlarmSuppression suppression = suppressionOpt.get();
            suppression.enable(operator);
            suppressionRepository.save(suppression);
            
            log.info("启用抑制规则成功: id={}", suppressionId);
            return true;
            
        } catch (Exception e) {
            log.error("启用抑制规则失败: id={}", suppressionId, e);
            throw new RuntimeException("启用抑制规则失败", e);
        }
    }
    
    @Override
    public boolean disableSuppression(SuppressionId suppressionId, String operator) {
        log.info("禁用抑制规则: id={}, operator={}", suppressionId, operator);
        
        try {
            Optional<AlarmSuppression> suppressionOpt = suppressionRepository.findById(suppressionId);
            if (suppressionOpt.isEmpty()) {
                throw new IllegalArgumentException("抑制规则不存在: " + suppressionId);
            }
            
            AlarmSuppression suppression = suppressionOpt.get();
            suppression.disable(operator);
            suppressionRepository.save(suppression);
            
            log.info("禁用抑制规则成功: id={}", suppressionId);
            return true;
            
        } catch (Exception e) {
            log.error("禁用抑制规则失败: id={}", suppressionId, e);
            throw new RuntimeException("禁用抑制规则失败", e);
        }
    }
    
    /**
     * 批量创建结果
     */
    public static class BatchCreateResult {
        private final int successCount;
        private final int failureCount;
        
        public BatchCreateResult(int successCount, int failureCount) {
            this.successCount = successCount;
            this.failureCount = failureCount;
        }
        
        public int getSuccessCount() { return successCount; }
        public int getFailureCount() { return failureCount; }
        public int getTotalCount() { return successCount + failureCount; }
    }
}
