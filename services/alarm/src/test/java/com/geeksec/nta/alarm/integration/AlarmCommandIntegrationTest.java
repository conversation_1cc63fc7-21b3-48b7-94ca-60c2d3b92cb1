package com.geeksec.nta.alarm.integration;

import com.geeksec.nta.alarm.application.service.AlarmCommandService;
import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.repository.AlarmRepository;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 告警命令集成测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@DisplayName("告警命令集成测试")
class AlarmCommandIntegrationTest {

    @Autowired
    private AlarmCommandService alarmCommandService;

    @Autowired
    private AlarmRepository alarmRepository;

    @Test
    @DisplayName("创建告警 - 完整流程测试")
    void createAlarm_FullFlow_Success() {
        // Given
        String alarmName = "集成测试告警";
        String alarmType = "SQL注入";
        Integer threatLevel = 3;
        Date alarmTime = new Date();
        Integer createdBy = 1;

        // When
        Alarm createdAlarm = alarmCommandService.createAlarm(
            alarmName, alarmType, threatLevel, alarmTime, createdBy
        );

        // Then
        assertNotNull(createdAlarm);
        assertNotNull(createdAlarm.getId());
        assertEquals(alarmName, createdAlarm.getAlarmName());
        assertEquals(alarmType, createdAlarm.getAlarmType());
        
        // 验证数据库中确实保存了告警
        Optional<Alarm> savedAlarm = alarmRepository.findById(
            new AlarmId(createdAlarm.getId())
        );
        assertTrue(savedAlarm.isPresent());
        assertEquals(alarmName, savedAlarm.get().getAlarmName());
    }

    @Test
    @DisplayName("更新告警状态 - 完整流程测试")
    void updateAlarmStatus_FullFlow_Success() {
        // Given - 先创建一个告警
        Alarm alarm = createTestAlarm();
        AlarmId alarmId = new AlarmId(alarm.getId());
        String newStatus = "已处理";
        Integer processor = 1;

        // When
        boolean result = alarmCommandService.updateAlarmStatus(
            alarmId, newStatus, processor
        );

        // Then
        assertTrue(result);
        
        // 验证数据库中的状态确实更新了
        Optional<Alarm> updatedAlarm = alarmRepository.findById(alarmId);
        assertTrue(updatedAlarm.isPresent());
        assertEquals(newStatus, updatedAlarm.get().getStatus());
    }

    @Test
    @DisplayName("删除告警 - 完整流程测试")
    void deleteAlarm_FullFlow_Success() {
        // Given - 先创建一个告警
        Alarm alarm = createTestAlarm();
        AlarmId alarmId = new AlarmId(alarm.getId());

        // When
        // TODO: 需要实现DeleteAlarmCommand
        // boolean result = alarmCommandService.deleteAlarm(deleteCommand);

        // Then
        // assertTrue(result);
        
        // 验证数据库中的告警确实被删除了
        // Optional<Alarm> deletedAlarm = alarmRepository.findById(alarmId);
        // assertFalse(deletedAlarm.isPresent());
    }

    @Test
    @DisplayName("批量处理告警 - 完整流程测试")
    void batchProcessAlarms_FullFlow_Success() {
        // Given - 创建多个告警
        Alarm alarm1 = createTestAlarm();
        Alarm alarm2 = createTestAlarm();
        
        // TODO: 实现批量处理逻辑
        // List<AlarmId> alarmIds = Arrays.asList(
        //     new AlarmId(alarm1.getId()),
        //     new AlarmId(alarm2.getId())
        // );

        // When
        // BatchProcessResult result = alarmCommandService.batchProcessAlarms(
        //     alarmIds, BatchAction.START_PROCESSING, 1
        // );

        // Then
        // assertNotNull(result);
        // assertEquals(2, result.totalCount());
        // assertEquals(2, result.successCount());
        // assertEquals(0, result.failureCount());
        // assertTrue(result.isAllSuccess());
    }

    // ==================== 辅助方法 ====================

    private Alarm createTestAlarm() {
        return alarmCommandService.createAlarm(
            "测试告警",
            "XSS攻击",
            2,
            new Date(),
            1
        );
    }
}
