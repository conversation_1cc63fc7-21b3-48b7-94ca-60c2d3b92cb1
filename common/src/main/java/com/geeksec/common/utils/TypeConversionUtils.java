package com.geeksec.common.utils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

/**
 * 类型转换工具类
 * 
 * 提供各种数据类型之间的安全转换方法，基于Guava和Apache Commons库
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public final class TypeConversionUtils {
    
    private TypeConversionUtils() {
        // 工具类，禁止实例化
    }
    
    // 注意：简单的类型转换请直接使用 Guava Primitives (Ints.tryParse, Longs.tryParse, Doubles.tryParse)
    // 这里只保留有特定业务逻辑的转换方法
    
    /**
     * 字符串转布尔值（使用Apache Commons Lang3）
     * 
     * @param str 字符串
     * @param defaultValue 默认值
     * @return 转换结果
     */
    public static boolean toBoolean(String str, boolean defaultValue) {
        if (StringUtils.isBlank(str)) {
            return defaultValue;
        }
        
        String trimmed = str.trim().toLowerCase();
        // 支持多种布尔值表示
        if ("true".equals(trimmed) || "1".equals(trimmed) || "yes".equals(trimmed) || "on".equals(trimmed)) {
            return true;
        } else if ("false".equals(trimmed) || "0".equals(trimmed) || "no".equals(trimmed) || "off".equals(trimmed)) {
            return false;
        } else {
            log.warn("字符串转布尔值失败: {}", str);
            return defaultValue;
        }
    }
    
    /**
     * 安全地将对象转换为整数
     * 
     * @param obj 对象
     * @param defaultValue 默认值
     * @return 转换后的整数，如果转换失败则返回默认值
     */
    public static int toInt(Object obj, int defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        
        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        }
        
        return toInt(obj.toString(), defaultValue);
    }
    
    /**
     * 安全地将对象转换为长整数
     * 
     * @param obj 对象
     * @param defaultValue 默认值
     * @return 转换后的长整数，如果转换失败则返回默认值
     */
    public static long toLong(Object obj, long defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        
        if (obj instanceof Number) {
            return ((Number) obj).longValue();
        }
        
        return toLong(obj.toString(), defaultValue);
    }
    
    /**
     * 安全地将对象转换为双精度浮点数
     * 
     * @param obj 对象
     * @param defaultValue 默认值
     * @return 转换后的双精度浮点数，如果转换失败则返回默认值
     */
    public static double toDouble(Object obj, double defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        
        if (obj instanceof Number) {
            return ((Number) obj).doubleValue();
        }
        
        return toDouble(obj.toString(), defaultValue);
    }
    
    /**
     * 安全地将对象转换为字符串
     * 
     * @param obj 对象
     * @param defaultValue 默认值
     * @return 转换后的字符串，如果对象为null则返回默认值
     */
    public static String toString(Object obj, String defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        
        return obj.toString();
    }
    
    /**
     * 字符串数组转整数列表（使用Guava Lists）
     * 
     * @param strArray 字符串数组
     * @return 整数列表
     */
    public static List<Integer> toIntList(String[] strArray) {
        if (strArray == null || strArray.length == 0) {
            return Lists.newArrayList();
        }
        
        return Arrays.stream(strArray)
                .filter(StringUtils::isNotBlank)
                .map(str -> toInt(str, 0))
                .collect(Collectors.toList());
    }
    
    /**
     * 逗号分隔字符串转整数列表（使用Guava Lists）
     * 
     * @param str 逗号分隔的字符串
     * @return 整数列表
     */
    public static List<Integer> toIntList(String str) {
        if (StringUtils.isBlank(str)) {
            return Lists.newArrayList();
        }
        
        return Arrays.stream(str.split(","))
                .filter(StringUtils::isNotBlank)
                .map(s -> toInt(s.trim(), 0))
                .collect(Collectors.toList());
    }
}