package com.geeksec.common.utils.network;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * HTTP工具类
 * 
 * 提供HTTP请求处理相关的工具方法，基于Apache Commons和Guava库
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public final class HttpUtils {
    
    private HttpUtils() {
        throw new UnsupportedOperationException("Utility class");
    }
    
    // IP地址正则表达式
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );
    
    // 私有IP地址范围
    private static final Pattern PRIVATE_IP_PATTERN = Pattern.compile(
        "^(10\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.|192\\.168\\.)"
    );
    
    /**
     * 获取客户端真实IP地址（使用Apache Commons Lang3）
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }
        
        // 检查各种可能的IP头
        String[] ipHeaders = {
            "X-Forwarded-For",
            "X-Real-IP", 
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };
        
        for (String header : ipHeaders) {
            String ip = request.getHeader(header);
            if (isValidIp(ip)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                if (isValidIp(ip)) {
                    return ip;
                }
            }
        }
        
        // 最后尝试getRemoteAddr
        String ip = request.getRemoteAddr();
        return isValidIp(ip) ? ip : "unknown";
    }
    
    /**
     * 验证IP地址是否有效
     * 
     * @param ip IP地址字符串
     * @return 是否有效
     */
    private static boolean isValidIp(String ip) {
        return StringUtils.isNotBlank(ip) 
            && !"unknown".equalsIgnoreCase(ip)
            && !"0:0:0:0:0:0:0:1".equals(ip)
            && IP_PATTERN.matcher(ip).matches();
    }
    
    /**
     * 判断是否为私有IP地址
     * 
     * @param ip IP地址
     * @return 是否为私有IP
     */
    public static boolean isPrivateIp(String ip) {
        if (StringUtils.isBlank(ip)) {
            return false;
        }
        
        return "127.0.0.1".equals(ip) 
            || "localhost".equalsIgnoreCase(ip)
            || PRIVATE_IP_PATTERN.matcher(ip).find();
    }
    
    // 注意：简单的HTTP操作请直接使用相关API
    // - 获取请求头：request.getHeader("headerName")
    // - URL编码：URLEncoder.encode(value, charset)
    // 这里只保留有特定业务逻辑的HTTP处理方法
    
    /**
     * 简单的HTTP GET请求
     * 
     * @param urlString 请求URL
     * @return 响应内容
     */
    public static String httpGet(String urlString) {
        if (StringUtils.isBlank(urlString)) {
            return null;
        }
        
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);
            
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    return response.toString();
                }
            } else {
                log.warn("HTTP请求失败，响应码: {}", responseCode);
                return null;
            }
        } catch (IOException e) {
            log.error("HTTP GET请求异常: {}", urlString, e);
            return null;
        }
    }
    
    /**
     * 判断是否为AJAX请求
     * 
     * @param request HTTP请求对象
     * @return 是否为AJAX请求
     */
    public static boolean isAjaxRequest(HttpServletRequest request) {
        if (request == null) {
            return false;
        }
        
        String requestedWith = request.getHeader("X-Requested-With");
        return "XMLHttpRequest".equals(requestedWith);
    }
    
    /**
     * 判断是否为移动设备请求
     * 
     * @param request HTTP请求对象
     * @return 是否为移动设备
     */
    public static boolean isMobileDevice(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null) {
            userAgent = userAgent.toLowerCase();
        }
        
        if (StringUtils.isBlank(userAgent)) {
            return false;
        }
        
        String[] mobileKeywords = {
            "mobile", "android", "iphone", "ipad", "ipod", 
            "blackberry", "windows phone", "opera mini"
        };
        
        for (String keyword : mobileKeywords) {
            if (userAgent.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
}