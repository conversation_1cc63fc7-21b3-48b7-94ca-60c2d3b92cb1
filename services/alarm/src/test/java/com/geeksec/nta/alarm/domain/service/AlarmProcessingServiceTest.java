package com.geeksec.nta.alarm.domain.service;

import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.enums.AlarmStatusEnum;
import com.geeksec.nta.alarm.domain.valueobject.ThreatLevel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AlarmProcessingService领域服务单元测试
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AlarmProcessingService领域服务测试")
class AlarmProcessingServiceTest {

    private AlarmProcessingService alarmProcessingService;

    @BeforeEach
    void setUp() {
        alarmProcessingService = new AlarmProcessingService();
    }

    @Test
    @DisplayName("开始处理告警 - 成功场景")
    void startProcessing_whenValidAlarm_thenSuccess() {
        // Given
        Alarm alarm = createTestAlarm();
        Integer processor = 1001;

        // When
        assertDoesNotThrow(() -> alarmProcessingService.startProcessing(alarm, processor));

        // Then
        assertEquals(AlarmStatusEnum.PROCESSING, alarm.getAlarmStatus());
        assertEquals(processor, alarm.getProcessor());
        assertNotNull(alarm.getProcessStartTime());
    }

    @Test
    @DisplayName("开始处理告警 - 告警不存在")
    void startProcessing_whenAlarmNotFound_thenReturnFalse() {
        // Given
        AlarmId alarmId = AlarmId.of("non-existent-id");
        Integer processor = 1001;
        
        when(alarmRepository.findById(alarmId)).thenReturn(Optional.empty());

        // When
        boolean result = alarmProcessingService.startProcessing(alarmId, processor);

        // Then
        assertFalse(result);
        verify(alarmRepository).findById(alarmId);
        verify(alarmRepository, never()).save(any());
    }

    @Test
    @DisplayName("开始处理告警 - 告警已在处理中")
    void startProcessing_whenAlarmAlreadyProcessing_thenThrowException() {
        // Given
        AlarmId alarmId = AlarmId.of("test-alarm-id");
        Integer processor = 1001;
        Alarm alarm = createTestAlarm();
        alarm.updateStatus(AlarmStatusEnum.PROCESSING, processor);
        
        when(alarmRepository.findById(alarmId)).thenReturn(Optional.of(alarm));

        // When & Then
        IllegalStateException exception = assertThrows(
            IllegalStateException.class,
            () -> alarmProcessingService.startProcessing(alarmId, processor)
        );
        assertEquals("告警已在处理中", exception.getMessage());
    }

    @Test
    @DisplayName("完成处理告警 - 成功场景")
    void completeProcessing_whenValidAlarm_thenSuccess() {
        // Given
        AlarmId alarmId = AlarmId.of("test-alarm-id");
        Integer processor = 1001;
        String processResult = "处理完成，已确认为误报";
        
        Alarm alarm = createTestAlarm();
        alarm.updateStatus(AlarmStatusEnum.PROCESSING, processor);
        
        when(alarmRepository.findById(alarmId)).thenReturn(Optional.of(alarm));
        when(alarmRepository.save(any(Alarm.class))).thenReturn(alarm);

        // When
        boolean result = alarmProcessingService.completeProcessing(alarmId, processor, processResult);

        // Then
        assertTrue(result);
        assertEquals(AlarmStatusEnum.COMPLETED, alarm.getAlarmStatus());
        assertEquals(processResult, alarm.getProcessResult());
        assertNotNull(alarm.getProcessEndTime());
        
        verify(alarmRepository).findById(alarmId);
        verify(alarmRepository).save(alarm);
    }

    @Test
    @DisplayName("完成处理告警 - 处理者不匹配")
    void completeProcessing_whenProcessorMismatch_thenThrowException() {
        // Given
        AlarmId alarmId = AlarmId.of("test-alarm-id");
        Integer originalProcessor = 1001;
        Integer differentProcessor = 1002;
        String processResult = "处理完成";
        
        Alarm alarm = createTestAlarm();
        alarm.updateStatus(AlarmStatusEnum.PROCESSING, originalProcessor);
        
        when(alarmRepository.findById(alarmId)).thenReturn(Optional.of(alarm));

        // When & Then
        IllegalStateException exception = assertThrows(
            IllegalStateException.class,
            () -> alarmProcessingService.completeProcessing(alarmId, differentProcessor, processResult)
        );
        assertEquals("只有当前处理者才能完成处理", exception.getMessage());
    }

    @Test
    @DisplayName("批量处理告警 - 成功场景")
    void batchProcessAlarms_whenValidAlarms_thenSuccess() {
        // Given
        List<AlarmId> alarmIds = Arrays.asList(
            AlarmId.of("alarm-1"),
            AlarmId.of("alarm-2")
        );
        Integer processor = 1001;
        AlarmProcessingService.BatchAction action = AlarmProcessingService.BatchAction.START_PROCESSING;
        
        List<Alarm> alarms = Arrays.asList(
            createTestAlarm(),
            createTestAlarm()
        );
        
        when(alarmRepository.findByIds(alarmIds)).thenReturn(alarms);
        when(alarmRepository.saveAll(anyList())).thenReturn(alarms);

        // When
        AlarmProcessingService.BatchProcessResult result = 
            alarmProcessingService.batchProcessAlarms(alarmIds, processor, action);

        // Then
        assertNotNull(result);
        assertEquals(2, result.totalCount());
        assertEquals(2, result.successCount());
        assertEquals(0, result.failureCount());
        assertTrue(result.failureReasons().isEmpty());
        
        // 验证所有告警状态都已更新
        alarms.forEach(alarm -> assertEquals(AlarmStatusEnum.PROCESSING, alarm.getAlarmStatus()));
        
        verify(alarmRepository).findByIds(alarmIds);
        verify(alarmRepository).saveAll(alarms);
    }

    @Test
    @DisplayName("批量处理告警 - 部分失败")
    void batchProcessAlarms_whenSomeAlarmsFail_thenPartialSuccess() {
        // Given
        List<AlarmId> alarmIds = Arrays.asList(
            AlarmId.of("alarm-1"),
            AlarmId.of("alarm-2")
        );
        Integer processor = 1001;
        AlarmProcessingService.BatchAction action = AlarmProcessingService.BatchAction.START_PROCESSING;
        
        Alarm validAlarm = createTestAlarm();
        Alarm processingAlarm = createTestAlarm();
        processingAlarm.updateStatus(AlarmStatusEnum.PROCESSING, processor); // 已在处理中
        
        List<Alarm> alarms = Arrays.asList(validAlarm, processingAlarm);
        
        when(alarmRepository.findByIds(alarmIds)).thenReturn(alarms);
        when(alarmRepository.saveAll(anyList())).thenReturn(Arrays.asList(validAlarm));

        // When
        AlarmProcessingService.BatchProcessResult result = 
            alarmProcessingService.batchProcessAlarms(alarmIds, processor, action);

        // Then
        assertNotNull(result);
        assertEquals(2, result.totalCount());
        assertEquals(1, result.successCount());
        assertEquals(1, result.failureCount());
        assertFalse(result.failureReasons().isEmpty());
        
        verify(alarmRepository).findByIds(alarmIds);
        verify(alarmRepository).saveAll(anyList());
    }

    @Test
    @DisplayName("自动升级威胁等级 - 成功场景")
    void autoUpgradeThreatLevel_whenValidConditions_thenSuccess() {
        // Given
        Alarm alarm = createTestAlarm();
        // 通过反射设置攻击等级为2（中等威胁）
        setAlarmAttackLevel(alarm, 2);

        // 添加多个攻击者IP
        addAttackerToAlarm(alarm, "*************");
        addAttackerToAlarm(alarm, "*************");
        addAttackerToAlarm(alarm, "*************");

        when(alarmRepository.save(any(Alarm.class))).thenReturn(alarm);

        // When
        boolean result = alarmProcessingService.autoUpgradeThreatLevel(alarm);

        // Then
        assertTrue(result);
        assertEquals(3, alarm.getAttackLevel()); // 应该升级到高威胁

        verify(alarmRepository).save(alarm);
    }

    @Test
    @DisplayName("自动升级威胁等级 - 不满足升级条件")
    void autoUpgradeThreatLevel_whenNotMeetConditions_thenNoUpgrade() {
        // Given
        Alarm alarm = createTestAlarm();
        alarm.setAttackLevel(2); // 中等威胁
        alarm.addAttackerIp("*************"); // 只有1个IP
        
        // When
        boolean result = alarmProcessingService.autoUpgradeThreatLevel(alarm);

        // Then
        assertFalse(result);
        assertEquals(2, alarm.getAttackLevel()); // 威胁等级不变
        
        verify(alarmRepository, never()).save(any());
    }

    @Test
    @DisplayName("自动升级威胁等级 - 已是最高等级")
    void autoUpgradeThreatLevel_whenAlreadyMaxLevel_thenNoUpgrade() {
        // Given
        Alarm alarm = createTestAlarm();
        alarm.setAttackLevel(4); // 已是最高威胁等级
        alarm.addAttackerIp("*************");
        alarm.addAttackerIp("*************");
        alarm.addAttackerIp("*************");
        
        // When
        boolean result = alarmProcessingService.autoUpgradeThreatLevel(alarm);

        // Then
        assertFalse(result);
        assertEquals(4, alarm.getAttackLevel()); // 威胁等级不变
        
        verify(alarmRepository, never()).save(any());
    }

    @Test
    @DisplayName("计算处理优先级 - 高威胁等级")
    void calculateProcessingPriority_whenHighThreat_thenHighPriority() {
        // Given
        Alarm alarm = createTestAlarm();
        alarm.setAttackLevel(4); // 严重威胁

        // When
        int priority = alarmProcessingService.calculateProcessingPriority(alarm);

        // Then
        assertTrue(priority >= 80); // 高优先级
    }

    @Test
    @DisplayName("计算处理优先级 - 低威胁等级")
    void calculateProcessingPriority_whenLowThreat_thenLowPriority() {
        // Given
        Alarm alarm = createTestAlarm();
        alarm.setAttackLevel(1); // 低威胁

        // When
        int priority = alarmProcessingService.calculateProcessingPriority(alarm);

        // Then
        assertTrue(priority <= 40); // 低优先级
    }

    @Test
    @DisplayName("验证处理权限 - 有效处理者")
    void validateProcessingPermission_whenValidProcessor_thenNoException() {
        // Given
        Alarm alarm = createTestAlarm();
        Integer processor = 1001;

        // When & Then
        assertDoesNotThrow(() -> 
            alarmProcessingService.validateProcessingPermission(alarm, processor)
        );
    }

    @Test
    @DisplayName("验证处理权限 - 处理者为空")
    void validateProcessingPermission_whenProcessorIsNull_thenThrowException() {
        // Given
        Alarm alarm = createTestAlarm();
        Integer processor = null;

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> alarmProcessingService.validateProcessingPermission(alarm, processor)
        );
        assertEquals("处理者不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("检查是否需要升级 - 满足条件")
    void shouldUpgradeThreatLevel_whenMeetConditions_thenReturnTrue() {
        // Given
        Alarm alarm = createTestAlarm();
        alarm.setAttackLevel(2);
        alarm.addAttackerIp("*************");
        alarm.addAttackerIp("*************");
        alarm.addAttackerIp("*************");

        // When
        boolean result = alarmProcessingService.shouldUpgradeThreatLevel(alarm);

        // Then
        assertTrue(result);
    }

    // ==================== 辅助方法 ====================

    private Alarm createTestAlarm() {
        return Alarm.create(
            "测试告警",
            "TEST_ATTACK",
            3,
            new Date(),
            1001
        );
    }
}
