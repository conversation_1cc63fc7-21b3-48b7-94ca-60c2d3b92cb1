package com.geeksec.nta.alarm.application.service;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.application.query.SuppressionListQuery;
import com.geeksec.nta.alarm.application.service.AlarmSuppressionQueryService;
import com.geeksec.nta.alarm.domain.aggregate.suppression.AlarmSuppression;
import com.geeksec.nta.alarm.domain.repository.AlarmSuppressionRepository;
import com.geeksec.nta.alarm.interfaces.converter.AlarmSuppressionConverter;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmSuppressionResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 告警抑制查询服务实现
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlarmSuppressionQueryServiceImpl implements AlarmSuppressionQueryService {

    private final AlarmSuppressionRepository suppressionRepository;
    private final AlarmSuppressionConverter suppressionConverter;

    @Override
    public PageResultVo<AlarmSuppressionResponse> listSuppressions(SuppressionListQuery query) {
        log.info("分页查询告警抑制规则，查询条件: {}", query);
        
        try {
            // 查询抑制规则列表
            List<AlarmSuppression> suppressions = suppressionRepository.findByQuery(query);
            
            // 查询总数
            long total = suppressionRepository.countByQuery(query);
            
            // 转换为响应对象
            List<AlarmSuppressionResponse> responses = suppressionConverter.toResponseList(suppressions);
            
            return new PageResultVo<>(responses, total, query.getPageNum(), query.getPageSize());
            
        } catch (Exception e) {
            log.error("查询告警抑制规则失败", e);
            throw new RuntimeException("查询告警抑制规则失败: " + e.getMessage(), e);
        }
    }

    @Override
    public AlarmSuppressionResponse getSuppressionById(Long suppressionId) {
        log.info("根据ID查询告警抑制规则: {}", suppressionId);
        
        try {
            AlarmSuppression suppression = suppressionRepository.findById(suppressionId.intValue());
            if (suppression == null) {
                log.warn("告警抑制规则不存在: {}", suppressionId);
                return null;
            }
            
            return suppressionConverter.toResponse(suppression);
            
        } catch (Exception e) {
            log.error("查询告警抑制规则失败: {}", suppressionId, e);
            throw new RuntimeException("查询告警抑制规则失败: " + e.getMessage(), e);
        }
    }
}