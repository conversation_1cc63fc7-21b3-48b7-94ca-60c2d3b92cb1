package com.geeksec.nta.alarm.domain.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 用户ID值对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
@ToString
@EqualsAndHashCode
public final class UserId {
    
    private final String value;
    
    private UserId(String value) {
        this.value = Objects.requireNonNull(value, "用户ID不能为空");
        if (value.trim().isEmpty()) {
            throw new IllegalArgumentException("用户ID不能为空字符串");
        }
    }
    
    /**
     * 创建用户ID
     * 
     * @param value ID值
     * @return 用户ID
     */
    public static UserId of(String value) {
        return new UserId(value);
    }
    
    /**
     * 创建用户ID
     * 
     * @param value ID值
     * @return 用户ID
     */
    public static UserId of(Integer value) {
        if (value == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return new UserId(value.toString());
    }
}
