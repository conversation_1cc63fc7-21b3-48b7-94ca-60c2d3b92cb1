package com.geeksec.nta.alarm.domain.exceptions;

/**
 * 告警领域异常基类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public abstract class AlarmDomainException extends RuntimeException {
    
    private final String errorCode;
    
    protected AlarmDomainException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    protected AlarmDomainException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}

/**
 * 告警不存在异常
 */
class AlarmNotFoundException extends AlarmDomainException {
    
    public AlarmNotFoundException(String alarmId) {
        super("ALARM_NOT_FOUND", "告警不存在: " + alarmId);
    }
}

/**
 * 告警状态无效异常
 */
class InvalidAlarmStatusException extends AlarmDomainException {
    
    public InvalidAlarmStatusException(String currentStatus, String targetStatus) {
        super("INVALID_ALARM_STATUS", 
              String.format("无法从状态 %s 转换到 %s", currentStatus, targetStatus));
    }
}

/**
 * 告警已被删除异常
 */
class AlarmAlreadyDeletedException extends AlarmDomainException {
    
    public AlarmAlreadyDeletedException(String alarmId) {
        super("ALARM_ALREADY_DELETED", "告警已被删除: " + alarmId);
    }
}

/**
 * 订阅不存在异常
 */
class SubscriptionNotFoundException extends AlarmDomainException {
    
    public SubscriptionNotFoundException(String subscriptionId) {
        super("SUBSCRIPTION_NOT_FOUND", "订阅不存在: " + subscriptionId);
    }
}

/**
 * 订阅名称重复异常
 */
class DuplicateSubscriptionNameException extends AlarmDomainException {
    
    public DuplicateSubscriptionNameException(String subscriptionName) {
        super("DUPLICATE_SUBSCRIPTION_NAME", "订阅名称已存在: " + subscriptionName);
    }
}

/**
 * 订阅规则无效异常
 */
class InvalidSubscriptionRuleException extends AlarmDomainException {
    
    public InvalidSubscriptionRuleException(String ruleDescription) {
        super("INVALID_SUBSCRIPTION_RULE", "订阅规则无效: " + ruleDescription);
    }
}

/**
 * 抑制规则不存在异常
 */
class SuppressionNotFoundException extends AlarmDomainException {
    
    public SuppressionNotFoundException(String suppressionId) {
        super("SUPPRESSION_NOT_FOUND", "抑制规则不存在: " + suppressionId);
    }
}

/**
 * 抑制规则重复异常
 */
class DuplicateSuppressionRuleException extends AlarmDomainException {
    
    public DuplicateSuppressionRuleException(String victim, String attacker, String label) {
        super("DUPLICATE_SUPPRESSION_RULE", 
              String.format("抑制规则已存在: %s->%s:%s", attacker, victim, label));
    }
}

/**
 * 抑制规则无效异常
 */
class InvalidSuppressionRuleException extends AlarmDomainException {
    
    public InvalidSuppressionRuleException(String ruleDescription) {
        super("INVALID_SUPPRESSION_RULE", "抑制规则无效: " + ruleDescription);
    }
}

/**
 * 元数据不存在异常
 */
class MetadataNotFoundException extends AlarmDomainException {
    
    public MetadataNotFoundException(String metadataType, String code) {
        super("METADATA_NOT_FOUND", 
              String.format("元数据不存在: type=%s, code=%s", metadataType, code));
    }
}

/**
 * 权限不足异常
 */
class InsufficientPermissionException extends AlarmDomainException {
    
    public InsufficientPermissionException(String operation) {
        super("INSUFFICIENT_PERMISSION", "权限不足，无法执行操作: " + operation);
    }
}

/**
 * 业务规则违反异常
 */
class BusinessRuleViolationException extends AlarmDomainException {
    
    public BusinessRuleViolationException(String rule, String details) {
        super("BUSINESS_RULE_VIOLATION", 
              String.format("违反业务规则 %s: %s", rule, details));
    }
}

/**
 * 资源冲突异常
 */
class ResourceConflictException extends AlarmDomainException {
    
    public ResourceConflictException(String resource, String reason) {
        super("RESOURCE_CONFLICT", 
              String.format("资源冲突 %s: %s", resource, reason));
    }
}

/**
 * 操作超时异常
 */
class OperationTimeoutException extends AlarmDomainException {
    
    public OperationTimeoutException(String operation, long timeoutMs) {
        super("OPERATION_TIMEOUT", 
              String.format("操作超时 %s: %dms", operation, timeoutMs));
    }
}

/**
 * 外部服务异常
 */
class ExternalServiceException extends AlarmDomainException {
    
    public ExternalServiceException(String service, String reason) {
        super("EXTERNAL_SERVICE_ERROR", 
              String.format("外部服务异常 %s: %s", service, reason));
    }
    
    public ExternalServiceException(String service, String reason, Throwable cause) {
        super("EXTERNAL_SERVICE_ERROR", 
              String.format("外部服务异常 %s: %s", service, reason), cause);
    }
}
