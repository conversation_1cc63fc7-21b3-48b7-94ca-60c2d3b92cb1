package com.geeksec.nta.alarm.domain;

import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.enums.AlarmStatusEnum;
import com.geeksec.nta.alarm.domain.valueobject.ThreatLevel;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 领域模型测试
 * 验证DDD重构后的核心领域模型是否正常工作
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@DisplayName("领域模型测试")
class DomainModelTest {

    @Test
    @DisplayName("创建告警 - 验证聚合根创建")
    void createAlarm_whenValidParameters_thenSuccess() {
        // Given
        String alarmName = "SQL注入攻击";
        String alarmType = "WEB_ATTACK";
        Integer threatLevel = 3;
        Date alarmTime = new Date();
        Integer createdBy = 1001;

        // When
        Alarm alarm = Alarm.create(alarmName, alarmType, threatLevel, alarmTime, createdBy);

        // Then
        assertNotNull(alarm);
        assertEquals(alarmName, alarm.getAlarmName());
        assertEquals(alarmType, alarm.getAlarmType());
        assertEquals(threatLevel, alarm.getAttackLevel());
        assertEquals(AlarmStatusEnum.UNPROCESSED, alarm.getAlarmStatus());
        assertEquals(createdBy, alarm.getCreatedBy());
        assertNotNull(alarm.getId());
        assertNotNull(alarm.getCreatedAt());
    }

    @Test
    @DisplayName("威胁等级值对象 - 验证创建和比较")
    void threatLevel_whenCreated_thenValidateCorrectly() {
        // Given & When
        ThreatLevel low = ThreatLevel.of(1);
        ThreatLevel medium = ThreatLevel.of(2);
        ThreatLevel high = ThreatLevel.of(3);
        ThreatLevel critical = ThreatLevel.of(4);

        // Then
        assertEquals(1, low.getLevel());
        assertEquals("低", low.getDescription());
        
        assertEquals(2, medium.getLevel());
        assertEquals("中", medium.getDescription());
        
        assertEquals(3, high.getLevel());
        assertEquals("高", high.getDescription());
        
        assertEquals(4, critical.getLevel());
        assertEquals("严重", critical.getDescription());

        // 验证比较逻辑
        assertTrue(medium.isHigherThan(low));
        assertTrue(high.isHigherThan(medium));
        assertTrue(critical.isHigherThan(high));
        
        assertFalse(low.isHigherThan(medium));
        assertFalse(medium.isHigherThan(high));
        assertFalse(high.isHigherThan(critical));
    }

    @Test
    @DisplayName("告警状态更新 - 验证状态转换")
    void updateAlarmStatus_whenValidTransition_thenSuccess() {
        // Given
        Alarm alarm = Alarm.create("测试告警", "TEST_ATTACK", 2, new Date(), 1001);
        Integer updatedBy = 1002;

        // When & Then - 从未处理到处理中
        assertDoesNotThrow(() -> alarm.updateStatus(AlarmStatusEnum.PROCESSING, updatedBy));
        assertEquals(AlarmStatusEnum.PROCESSING, alarm.getAlarmStatus());
        assertEquals(updatedBy, alarm.getUpdatedBy());

        // When & Then - 从处理中到已完成
        assertDoesNotThrow(() -> alarm.updateStatus(AlarmStatusEnum.COMPLETED, updatedBy));
        assertEquals(AlarmStatusEnum.COMPLETED, alarm.getAlarmStatus());
    }

    @Test
    @DisplayName("告警状态更新 - 验证非法状态转换")
    void updateAlarmStatus_whenInvalidTransition_thenThrowException() {
        // Given
        Alarm alarm = Alarm.create("测试告警", "TEST_ATTACK", 2, new Date(), 1001);
        Integer updatedBy = 1002;

        // When & Then - 尝试从未处理直接到已完成（非法转换）
        IllegalStateException exception = assertThrows(
            IllegalStateException.class,
            () -> alarm.updateStatus(AlarmStatusEnum.COMPLETED, updatedBy)
        );
        
        assertTrue(exception.getMessage().contains("告警状态不能从"));
        assertEquals(AlarmStatusEnum.UNPROCESSED, alarm.getAlarmStatus()); // 状态应该保持不变
    }

    @Test
    @DisplayName("威胁等级提升 - 验证业务规则")
    void upgradeThreatLevel_whenValidUpgrade_thenSuccess() {
        // Given
        Alarm alarm = Alarm.create("测试告警", "TEST_ATTACK", 2, new Date(), 1001);
        ThreatLevel newLevel = ThreatLevel.of(3);
        Integer updatedBy = 1002;

        // When
        assertDoesNotThrow(() -> alarm.upgradeThreatLevel(newLevel, updatedBy));

        // Then
        assertEquals(3, alarm.getAttackLevel());
        assertEquals(updatedBy, alarm.getUpdatedBy());
    }

    @Test
    @DisplayName("威胁等级提升 - 验证不能降级")
    void upgradeThreatLevel_whenDowngrade_thenThrowException() {
        // Given
        Alarm alarm = Alarm.create("测试告警", "TEST_ATTACK", 3, new Date(), 1001);
        ThreatLevel lowerLevel = ThreatLevel.of(2);
        Integer updatedBy = 1002;

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> alarm.upgradeThreatLevel(lowerLevel, updatedBy)
        );
        
        assertEquals("新威胁等级必须高于当前等级", exception.getMessage());
        assertEquals(3, alarm.getAttackLevel()); // 等级应该保持不变
    }

    @Test
    @DisplayName("威胁等级值对象 - 验证边界值")
    void threatLevel_whenInvalidLevel_thenThrowException() {
        // When & Then - 测试无效等级
        assertThrows(IllegalArgumentException.class, () -> ThreatLevel.of(0));
        assertThrows(IllegalArgumentException.class, () -> ThreatLevel.of(5));
        assertThrows(IllegalArgumentException.class, () -> ThreatLevel.of(-1));
        
        // 验证有效等级
        assertDoesNotThrow(() -> ThreatLevel.of(1));
        assertDoesNotThrow(() -> ThreatLevel.of(2));
        assertDoesNotThrow(() -> ThreatLevel.of(3));
        assertDoesNotThrow(() -> ThreatLevel.of(4));
    }

    @Test
    @DisplayName("告警创建 - 验证必填字段")
    void createAlarm_whenMissingRequiredFields_thenThrowException() {
        // Given
        Date alarmTime = new Date();
        Integer createdBy = 1001;

        // When & Then - 测试空告警名称
        assertThrows(IllegalArgumentException.class, 
            () -> Alarm.create(null, "TEST_ATTACK", 2, alarmTime, createdBy));
        
        assertThrows(IllegalArgumentException.class, 
            () -> Alarm.create("", "TEST_ATTACK", 2, alarmTime, createdBy));

        // When & Then - 测试空告警类型
        assertThrows(IllegalArgumentException.class, 
            () -> Alarm.create("测试告警", null, 2, alarmTime, createdBy));
        
        assertThrows(IllegalArgumentException.class, 
            () -> Alarm.create("测试告警", "", 2, alarmTime, createdBy));

        // When & Then - 测试无效威胁等级
        assertThrows(IllegalArgumentException.class, 
            () -> Alarm.create("测试告警", "TEST_ATTACK", 0, alarmTime, createdBy));

        // When & Then - 测试空时间
        assertThrows(NullPointerException.class, 
            () -> Alarm.create("测试告警", "TEST_ATTACK", 2, null, createdBy));
    }

    @Test
    @DisplayName("告警业务方法 - 验证兼容性方法")
    void alarmCompatibilityMethods_whenCalled_thenWorkCorrectly() {
        // Given
        Alarm alarm = Alarm.create("测试告警", "TEST_ATTACK", 2, new Date(), 1001);

        // When & Then - 测试setStatus兼容方法
        assertDoesNotThrow(() -> alarm.setStatus(1)); // 设置为处理中
        assertEquals(AlarmStatusEnum.PROCESSING, alarm.getAlarmStatus());

        // When & Then - 测试getIndex兼容方法
        String index = alarm.getIndex();
        assertNotNull(index);
        assertEquals(alarm.getId().getValue(), index);
    }
}
