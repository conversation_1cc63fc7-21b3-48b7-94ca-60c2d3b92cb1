package com.geeksec.nta.alarm.domain.aggregate.alarm;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.geeksec.nta.alarm.domain.event.AlarmStatusChangedEvent;
import com.geeksec.nta.alarm.domain.event.AlarmThreatLevelChangedEvent;
import com.geeksec.nta.alarm.domain.valueobject.AlarmAttacker;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.geeksec.nta.alarm.domain.valueobject.AlarmStatusEnum;
import com.geeksec.nta.alarm.domain.valueobject.AlarmTargets;
import com.geeksec.nta.alarm.domain.valueobject.AlarmVictim;
import com.geeksec.nta.alarm.domain.valueobject.ThreatLevel;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.handler.JacksonTypeHandler;

import lombok.Getter;
import lombok.Setter;

/**
 * 告警聚合根 - 对应alarms表
 *
 * 告警是网络安全检测的核心聚合，包含了威胁检测的完整信息
 * 包括攻击者、受害者、威胁等级、处理状态等关键信息
 *
 * <AUTHOR>
 * @Description：告警管理的核心聚合根，封装告警的业务逻辑和不变性约束
 */
@Getter
@Table(value = "alarms", schema = "nta", comment = "主告警表")
public class Alarm {

    // ==================== 聚合根标识 ====================

    @Id(keyType = KeyType.Generator)
    @Column(value = "id", comment = "告警唯一标识")
    private String id;

    // ==================== 核心告警信息 ====================

    @Column(value = "task_id", comment = "关联任务ID")
    @Setter
    private Long taskId;

    @Column(value = "alarm_time", comment = "告警发生时间")
    private Date alarmTime;

    @Column(value = "alarm_name", comment = "告警名称")
    private String alarmName;

    @Column(value = "alarm_type", comment = "告警类型")
    private String alarmType;

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @Column(typeHandler = JacksonTypeHandler.class, value = "targets", comment = "告警对象")
    private List<AlarmTargets> targets;

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @Column(typeHandler = JacksonTypeHandler.class, value = "victims", comment = "受害者")
    private List<AlarmVictim> victims;

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @Column(typeHandler = JacksonTypeHandler.class, value = "attackers", comment = "攻击者")
    private List<AlarmAttacker> attackers;

    @Column(value = "attack_level", comment = "威胁权重/攻击等级")
    private Integer attackLevel;

    @Column(value = "status", comment = "处理状态：0-未处理，1-处理中，2-已处理，3-已关闭")
    private Integer status;

    @Column(typeHandler = JacksonTypeHandler.class, value = "raw_data", comment = "告警原始数据")
    private String rawData;

    @Column(value = "knowledge_id", comment = "关联告警知识库ID")
    private Long knowledgeId;

    @Column(value = "attack_chain_names", comment = "攻击链名称数组")
    private List<String> attackChainNames;

    @Column(value = "created_at", comment = "创建时间")
    private LocalDateTime createdAt;

    @Column(value = "updated_at", comment = "更新时间")
    private LocalDateTime updatedAt;

    @Column(value = "created_by", comment = "创建者用户ID")
    private Integer createdBy;

    @Column(value = "updated_by", comment = "更新者用户ID")
    private Integer updatedBy;

    // ==================== 领域事件 ====================

    private final List<Object> domainEvents = new ArrayList<>();

    // ==================== 构造函数 ====================

    /**
     * 默认构造函数（用于持久化框架）
     */
    protected Alarm() {
        // 用于MyBatis等持久化框架
    }

    // ==================== 业务方法 ====================

    /**
     * 获取告警ID
     *
     * @return 告警ID值对象
     */
    public AlarmId getAlarmId() {
        return AlarmId.of(this.id);
    }

    /**
     * 获取告警状态
     *
     * @return 告警状态枚举
     */
    public AlarmStatusEnum getAlarmStatus() {
        return AlarmStatusEnum.fromCode(this.status);
    }

    /**
     * 获取威胁等级
     *
     * @return 威胁等级值对象
     */
    public ThreatLevel getThreatLevel() {
        return ThreatLevel.fromLevel(this.attackLevel);
    }

    /**
     * 更新告警状态
     *
     * @param newStatus 新状态
     * @param updatedBy 更新者
     */
    public void updateStatus(AlarmStatusEnum newStatus, Integer updatedBy) {
        Objects.requireNonNull(newStatus, "新状态不能为空");

        AlarmStatusEnum currentStatus = getAlarmStatus();

        // 检查状态转换是否合法
        if (!currentStatus.canTransitionTo(newStatus)) {
            throw new IllegalStateException(
                    String.format("告警状态不能从 %s 转换到 %s",
                            currentStatus.getDescription(), newStatus.getDescription()));
        }

        AlarmStatusEnum oldStatus = currentStatus;
        this.status = newStatus.getCode();
        this.updatedAt = LocalDateTime.now();
        this.updatedBy = updatedBy;

        // 发布状态变更事件
        addDomainEvent(new AlarmStatusChangedEvent(
                this.id,
                oldStatus,
                newStatus,
                updatedBy != null ? updatedBy.toString() : null));
    }

    /**
     * 提升威胁等级
     *
     * @param newLevel  新威胁等级
     * @param updatedBy 更新者
     */
    public void upgradeThreatLevel(ThreatLevel newLevel, Integer updatedBy) {
        Objects.requireNonNull(newLevel, "新威胁等级不能为空");

        ThreatLevel currentLevel = getThreatLevel();

        if (!newLevel.isHigherThan(currentLevel)) {
            throw new IllegalArgumentException("新威胁等级必须高于当前等级");
        }

        this.attackLevel = newLevel.getLevel();
        this.updatedAt = LocalDateTime.now();
        this.updatedBy = updatedBy;

        // 发布威胁等级变更事件
        addDomainEvent(new AlarmThreatLevelChangedEvent(
                this.id,
                currentLevel,
                newLevel,
                updatedBy != null ? updatedBy.toString() : null,
                "威胁等级提升"));
    }

    /**
     * 添加攻击目标
     *
     * @param target 攻击目标
     */
    public void addTarget(AlarmTargets target) {
        Objects.requireNonNull(target, "攻击目标不能为空");

        if (this.targets == null) {
            this.targets = new ArrayList<>();
        }

        this.targets.add(target);
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 添加受害者
     *
     * @param victim 受害者
     */
    public void addVictim(AlarmVictim victim) {
        Objects.requireNonNull(victim, "受害者不能为空");

        if (this.victims == null) {
            this.victims = new ArrayList<>();
        }

        this.victims.add(victim);
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 添加攻击者
     *
     * @param attacker 攻击者
     */
    public void addAttacker(AlarmAttacker attacker) {
        Objects.requireNonNull(attacker, "攻击者不能为空");

        if (this.attackers == null) {
            this.attackers = new ArrayList<>();
        }

        this.attackers.add(attacker);
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 关联知识库
     *
     * @param knowledgeId 知识库ID
     * @param updatedBy   更新者
     */
    public void associateKnowledge(Long knowledgeId, Integer updatedBy) {
        Objects.requireNonNull(knowledgeId, "知识库ID不能为空");

        this.knowledgeId = knowledgeId;
        this.updatedAt = LocalDateTime.now();
        this.updatedBy = updatedBy;
    }

    /**
     * 添加攻击链
     *
     * @param attackChainName 攻击链名称
     */
    public void addAttackChain(String attackChainName) {
        Objects.requireNonNull(attackChainName, "攻击链名称不能为空");

        if (attackChainName.trim().isEmpty()) {
            throw new IllegalArgumentException("攻击链名称不能为空字符串");
        }

        if (this.attackChainNames == null) {
            this.attackChainNames = new ArrayList<>();
        }

        if (!this.attackChainNames.contains(attackChainName.trim())) {
            this.attackChainNames.add(attackChainName.trim());
            this.updatedAt = LocalDateTime.now();
        }
    }

    /**
     * 设置原始数据
     *
     * @param rawData   原始数据
     * @param updatedBy 更新者
     */
    public void setRawData(String rawData, Integer updatedBy) {
        this.rawData = rawData;
        this.updatedAt = LocalDateTime.now();
        this.updatedBy = updatedBy;
    }

    // ==================== 查询方法 ====================

    /**
     * 是否为高威胁告警
     *
     * @return 是否为高威胁
     */
    public boolean isHighThreat() {
        return getThreatLevel().isHighThreat();
    }

    /**
     * 是否为严重威胁告警
     *
     * @return 是否为严重威胁
     */
    public boolean isCriticalThreat() {
        return getThreatLevel().isCritical();
    }

    /**
     * 是否已处理
     *
     * @return 是否已处理
     */
    public boolean isProcessed() {
        return getAlarmStatus() == AlarmStatusEnum.PROCESSED || getAlarmStatus() == AlarmStatusEnum.CLOSED;
    }

    /**
     * 是否已关闭
     *
     * @return 是否已关闭
     */
    public boolean isClosed() {
        return getAlarmStatus() == AlarmStatusEnum.CLOSED;
    }

    /**
     * 获取受害者IP列表
     *
     * @return 受害者IP列表
     */
    public List<String> getVictimIps() {
        if (victims == null || victims.isEmpty()) {
            return List.of();
        }
        return victims.stream()
                .map(AlarmVictim::getIp)
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * 获取攻击者IP列表
     *
     * @return 攻击者IP列表
     */
    public List<String> getAttackerIps() {
        if (attackers == null || attackers.isEmpty()) {
            return List.of();
        }
        return attackers.stream()
                .map(AlarmAttacker::getIp)
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * 是否包含指定攻击链
     *
     * @param attackChainName 攻击链名称
     * @return 是否包含
     */
    public boolean hasAttackChain(String attackChainName) {
        return attackChainNames != null && attackChainNames.contains(attackChainName);
    }

    // ==================== 领域事件处理 ====================

    /**
     * 添加领域事件
     *
     * @param event 领域事件
     */
    private void addDomainEvent(Object event) {
        this.domainEvents.add(event);
    }

    /**
     * 获取领域事件
     *
     * @return 领域事件列表
     */
    public List<Object> getDomainEvents() {
        return List.copyOf(domainEvents);
    }

    /**
     * 清除领域事件
     */
    public void clearDomainEvents() {
        this.domainEvents.clear();
    }

    // ==================== 不变性约束 ====================

    /**
     * 验证告警数据的完整性
     */
    public void validate() {
        if (alarmName == null || alarmName.trim().isEmpty()) {
            throw new IllegalStateException("告警名称不能为空");
        }
        if (alarmType == null || alarmType.trim().isEmpty()) {
            throw new IllegalStateException("告警类型不能为空");
        }
        if (alarmTime == null) {
            throw new IllegalStateException("告警时间不能为空");
        }
        if (attackLevel == null || attackLevel < 1 || attackLevel > 4) {
            throw new IllegalStateException("威胁等级必须在1-4之间");
        }
        if (status == null || status < 0 || status > 3) {
            throw new IllegalStateException("告警状态必须在0-3之间");
        }
    }

    // ==================== 兼容性方法 ====================

    /**
     * 设置原始数据（兼容旧代码）
     *
     * @param rawData 原始数据
     */
    public void setRawData(String rawData) {
        this.rawData = rawData;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 设置状态（兼容旧代码）
     *
     * @param status 状态码
     */
    public void setStatus(Integer status) {
        if (status != null) {
            AlarmStatusEnum newStatus = AlarmStatusEnum.fromCode(status);
            updateStatus(newStatus, this.updatedBy);
        }
    }

    /**
     * 设置创建时间（兼容基础设施层）
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createdAt = createTime;
    }

    /**
     * 获取索引（兼容基础设施层）
     *
     * @return 索引值
     */
    public String getIndex() {
        // 返回告警ID作为索引
        return this.id;
    }
}