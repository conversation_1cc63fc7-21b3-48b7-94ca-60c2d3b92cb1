package com.geeksec.nta.alarm.application.service;

import com.geeksec.nta.alarm.application.command.CreateSuppressionCommand;
import com.geeksec.nta.alarm.application.command.DeleteSuppressionCommand;
import com.geeksec.nta.alarm.application.command.UpdateSuppressionCommand;
import com.geeksec.nta.alarm.domain.aggregate.suppression.AlarmSuppression;
import com.geeksec.nta.alarm.domain.repository.AlarmSuppressionRepository;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 告警抑制命令服务测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("告警抑制命令服务测试")
class AlarmSuppressionCommandServiceImplTest {

    @Mock
    private AlarmSuppressionRepository suppressionRepository;

    @InjectMocks
    private AlarmSuppressionCommandServiceImpl suppressionCommandService;

    private CreateSuppressionCommand createCommand;
    private UpdateSuppressionCommand updateCommand;
    private DeleteSuppressionCommand deleteCommand;
    private AlarmSuppression testSuppression;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        createCommand = CreateSuppressionCommand.builder()
            .victim("*************")
            .attacker("********")
            .label("SQL注入")
            .suppressionName("测试抑制规则")
            .description("测试抑制规则描述")
            .enabled(true)
            .expiryTime(LocalDateTime.now().plusDays(30))
            .operator("testuser")
            .note("测试备注")
            .build();

        updateCommand = UpdateSuppressionCommand.builder()
            .suppressionId(new SuppressionId("suppression-123"))
            .victim("*************")
            .attacker("********")
            .label("XSS攻击")
            .suppressionName("更新的抑制规则")
            .description("更新的抑制规则描述")
            .enabled(false)
            .expiryTime(LocalDateTime.now().plusDays(60))
            .operator("testuser")
            .note("更新备注")
            .build();

        deleteCommand = DeleteSuppressionCommand.builder()
            .suppressionId(new SuppressionId("suppression-123"))
            .operator("testuser")
            .reason("不再需要")
            .build();

        testSuppression = createTestSuppression();
    }

    @Test
    @DisplayName("创建抑制规则 - 成功")
    void createSuppression_Success() {
        // Given
        when(suppressionRepository.save(any(AlarmSuppression.class)))
            .thenReturn(testSuppression);

        // When
        AlarmSuppression result = suppressionCommandService.createSuppression(createCommand);

        // Then
        assertNotNull(result);
        verify(suppressionRepository).save(any(AlarmSuppression.class));
    }

    @Test
    @DisplayName("更新抑制规则 - 成功")
    void updateSuppression_Success() {
        // Given
        when(suppressionRepository.findById(updateCommand.getSuppressionId()))
            .thenReturn(Optional.of(testSuppression));
        when(suppressionRepository.save(any(AlarmSuppression.class)))
            .thenReturn(testSuppression);

        // When
        AlarmSuppression result = suppressionCommandService.updateSuppression(updateCommand);

        // Then
        assertNotNull(result);
        verify(suppressionRepository).findById(updateCommand.getSuppressionId());
        verify(suppressionRepository).save(testSuppression);
    }

    @Test
    @DisplayName("更新抑制规则 - 规则不存在应抛出异常")
    void updateSuppression_NotFound_ShouldThrowException() {
        // Given
        when(suppressionRepository.findById(updateCommand.getSuppressionId()))
            .thenReturn(Optional.empty());

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            suppressionCommandService.updateSuppression(updateCommand);
        });
        
        verify(suppressionRepository).findById(updateCommand.getSuppressionId());
        verify(suppressionRepository, never()).save(any());
    }

    @Test
    @DisplayName("删除抑制规则 - 成功")
    void deleteSuppression_Success() {
        // Given
        when(suppressionRepository.findById(deleteCommand.getSuppressionId()))
            .thenReturn(Optional.of(testSuppression));

        // When
        boolean result = suppressionCommandService.deleteSuppression(deleteCommand);

        // Then
        assertTrue(result);
        verify(suppressionRepository).findById(deleteCommand.getSuppressionId());
        verify(suppressionRepository).delete(testSuppression);
    }

    @Test
    @DisplayName("删除抑制规则 - 规则不存在应返回false")
    void deleteSuppression_NotFound_ShouldReturnFalse() {
        // Given
        when(suppressionRepository.findById(deleteCommand.getSuppressionId()))
            .thenReturn(Optional.empty());

        // When
        boolean result = suppressionCommandService.deleteSuppression(deleteCommand);

        // Then
        assertFalse(result);
        verify(suppressionRepository).findById(deleteCommand.getSuppressionId());
        verify(suppressionRepository, never()).delete(any());
    }

    // ==================== 辅助方法 ====================

    private AlarmSuppression createTestSuppression() {
        return AlarmSuppression.create(
            "*************",
            "********", 
            "SQL注入",
            "测试抑制规则",
            "测试抑制规则描述",
            LocalDateTime.now().plusDays(30),
            "testuser"
        );
    }
}
