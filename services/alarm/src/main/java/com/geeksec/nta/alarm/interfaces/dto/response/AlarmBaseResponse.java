package com.geeksec.nta.alarm.interfaces.dto.response;

import com.geeksec.common.enums.AlarmHandlingStatus;
import com.geeksec.common.enums.AlarmTypeEnum;
import com.geeksec.common.enums.ThreatLevelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 告警响应基础类
 * 包含所有告警响应的公共字段
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Schema(description = "告警响应基础信息")
public abstract class AlarmBaseResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "告警ID")
    private String alarmId;

    @Schema(description = "告警名称")
    private String alarmName;

    @Schema(description = "告警类型")
    private AlarmTypeEnum alarmType;

    @Schema(description = "威胁等级")
    private ThreatLevelEnum threatLevel;

    @Schema(description = "告警状态")
    private AlarmHandlingStatus status;

    @Schema(description = "告警时间")
    private LocalDateTime alarmTime;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "创建者ID")
    private Integer createdBy;

    @Schema(description = "更新者ID")
    private Integer updatedBy;

    @Schema(description = "告警描述")
    private String description;

    @Schema(description = "攻击者IP")
    private String attackerIp;

    @Schema(description = "受害者IP")
    private String victimIp;

    @Schema(description = "攻击者端口")
    private Integer attackerPort;

    @Schema(description = "受害者端口")
    private Integer victimPort;

    @Schema(description = "协议类型")
    private String protocol;

    @Schema(description = "处理备注")
    private String processRemark;

    @Schema(description = "关闭原因")
    private String closeReason;

    @Schema(description = "扩展属性")
    private Map<String, Object> extendedProperties;
}
