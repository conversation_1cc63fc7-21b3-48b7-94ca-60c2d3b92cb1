package com.geeksec.nta.alarm.domain.event;

import com.geeksec.nta.alarm.domain.valueobject.AlarmStatusEnum;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 告警状态变更事件
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
@ToString
public final class AlarmStatusChangedEvent implements DomainEvent {

    /**
     * 告警ID
     */
    private final String alarmId;

    /**
     * 原状态
     */
    private final AlarmStatusEnum oldStatus;

    /**
     * 新状态
     */
    private final AlarmStatusEnum newStatus;

    /**
     * 操作人
     */
    private final String operator;

    /**
     * 事件发生时间
     */
    private final LocalDateTime eventTime;

    public AlarmStatusChangedEvent(String alarmId, AlarmStatusEnum oldStatus,
                                 AlarmStatusEnum newStatus, String operator) {
        this.alarmId = Objects.requireNonNull(alarmId, "告警ID不能为空");
        this.oldStatus = Objects.requireNonNull(oldStatus, "原状态不能为空");
        this.newStatus = Objects.requireNonNull(newStatus, "新状态不能为空");
        this.operator = operator;
        this.eventTime = LocalDateTime.now();
    }

    @Override
    public String getEventType() {
        return "AlarmStatusChanged";
    }

    @Override
    public String getAggregateId() {
        return alarmId;
    }

    @Override
    public LocalDateTime getEventTime() {
        return eventTime;
    }
}
