package com.geeksec.admin.infrastructure.service;

import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * 动态库检测服务接口
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public interface LibraryCheckService {

    /**
     * 检测动态库文件
     *
     * @param ruleId 规则ID
     * @return 检测结果
     */
    ObjectNode checkSoFiles(Integer ruleId);

    /**
     * 检测Docker动态库文件
     *
     * @param path 路径
     * @return 检测结果
     */
    ObjectNode checkDockerSoFiles(String path);

    /**
     * 检测系统动态库依赖
     *
     * @param libraryPath 库文件路径
     * @return 检测结果
     */
    ObjectNode checkLibraryDependencies(String libraryPath);

    /**
     * 获取系统已安装的动态库列表
     *
     * @return 动态库列表
     */
    ObjectNode getInstalledLibraries();
}
