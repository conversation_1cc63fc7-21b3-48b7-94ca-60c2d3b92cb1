package com.geeksec.nta.alarm.application.service;

import com.geeksec.nta.alarm.application.service.AlarmCommandService;
import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.enums.AlarmStatusEnum;
import com.geeksec.nta.alarm.domain.repository.AlarmRepository;
import com.geeksec.nta.alarm.domain.service.AlarmProcessingService;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AlarmCommandService应用服务单元测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AlarmCommandService应用服务测试")
class AlarmCommandServiceImplTest {

    @Mock
    private AlarmRepository alarmRepository;

    @Mock
    private AlarmProcessingService alarmProcessingService;

    private AlarmCommandService alarmCommandService;

    @BeforeEach
    void setUp() {
        alarmCommandService = new AlarmCommandServiceImpl(alarmRepository, alarmProcessingService);
    }

    @Test
    @DisplayName("创建告警 - 成功场景")
    void createAlarm_whenValidParameters_thenSuccess() {
        // Given
        String alarmName = "SQL注入攻击";
        String alarmType = "WEB_ATTACK";
        Integer threatLevel = 3;
        Date alarmTime = new Date();
        Integer createdBy = 1001;

        Alarm expectedAlarm = Alarm.create(alarmName, alarmType, threatLevel, alarmTime, createdBy);
        when(alarmRepository.save(any(Alarm.class))).thenReturn(expectedAlarm);

        // When
        Alarm result = alarmCommandService.createAlarm(alarmName, alarmType, threatLevel, alarmTime, createdBy);

        // Then
        assertNotNull(result);
        assertEquals(alarmName, result.getAlarmName());
        assertEquals(alarmType, result.getAlarmType());
        assertEquals(threatLevel, result.getAttackLevel());
        assertEquals(AlarmStatusEnum.UNPROCESSED, result.getAlarmStatus());
        
        verify(alarmRepository).save(any(Alarm.class));
    }

    @Test
    @DisplayName("创建告警 - 告警名称为空")
    void createAlarm_whenAlarmNameIsNull_thenThrowException() {
        // Given
        String alarmName = null;
        String alarmType = "WEB_ATTACK";
        Integer threatLevel = 3;
        Date alarmTime = new Date();
        Integer createdBy = 1001;

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> alarmCommandService.createAlarm(alarmName, alarmType, threatLevel, alarmTime, createdBy)
        );
        assertEquals("告警名称不能为空", exception.getMessage());
        
        verify(alarmRepository, never()).save(any());
    }

    @Test
    @DisplayName("开始处理告警 - 成功场景")
    void startProcessing_whenValidAlarm_thenSuccess() {
        // Given
        AlarmId alarmId = AlarmId.of("test-alarm-id");
        Integer processor = 1001;
        Alarm alarm = createTestAlarm();
        
        when(alarmRepository.findById(alarmId)).thenReturn(Optional.of(alarm));
        when(alarmRepository.save(any(Alarm.class))).thenReturn(alarm);

        // When
        boolean result = alarmCommandService.startProcessing(alarmId, processor);

        // Then
        assertTrue(result);
        verify(alarmRepository).findById(alarmId);
        verify(alarmProcessingService).startProcessing(alarm, processor);
        verify(alarmRepository).save(alarm);
    }

    @Test
    @DisplayName("开始处理告警 - 告警不存在")
    void startProcessing_whenAlarmNotFound_thenReturnFalse() {
        // Given
        AlarmId alarmId = AlarmId.of("non-existent-id");
        Integer processor = 1001;
        
        when(alarmRepository.findById(alarmId)).thenReturn(Optional.empty());

        // When
        boolean result = alarmCommandService.startProcessing(alarmId, processor);

        // Then
        assertFalse(result);
        verify(alarmRepository).findById(alarmId);
        verify(alarmProcessingService, never()).startProcessing(any(), any());
        verify(alarmRepository, never()).save(any());
    }

    @Test
    @DisplayName("完成处理告警 - 成功场景")
    void completeProcessing_whenValidAlarm_thenSuccess() {
        // Given
        AlarmId alarmId = AlarmId.of("test-alarm-id");
        Integer processor = 1001;
        String processResult = "处理完成，已确认为误报";
        
        Alarm alarm = createTestAlarm();
        alarm.updateStatus(AlarmStatusEnum.PROCESSING, processor);
        
        when(alarmRepository.findById(alarmId)).thenReturn(Optional.of(alarm));
        when(alarmRepository.save(any(Alarm.class))).thenReturn(alarm);

        // When
        boolean result = alarmCommandService.completeProcessing(alarmId, processor, processResult);

        // Then
        assertTrue(result);
        verify(alarmRepository).findById(alarmId);
        verify(alarmProcessingService).completeProcessing(eq(alarm), eq(processor), any());
        verify(alarmRepository).save(alarm);
    }

    @Test
    @DisplayName("批量处理告警 - 成功场景")
    void batchProcessAlarms_whenValidAlarms_thenSuccess() {
        // Given
        List<AlarmId> alarmIds = Arrays.asList(
            AlarmId.of("alarm-1"),
            AlarmId.of("alarm-2")
        );
        Integer processor = 1001;
        AlarmCommandService.BatchAction action = AlarmCommandService.BatchAction.START_PROCESSING;
        
        List<Alarm> alarms = Arrays.asList(
            createTestAlarm(),
            createTestAlarm()
        );
        
        when(alarmRepository.findByIds(alarmIds)).thenReturn(alarms);
        when(alarmProcessingService.batchProcess(eq(alarms), eq(processor), any()))
            .thenReturn(new AlarmProcessingService.BatchProcessResult(2, 0, null));
        when(alarmRepository.saveAll(alarms)).thenReturn(alarms);

        // When
        AlarmCommandService.BatchProcessResult result = 
            alarmCommandService.batchProcessAlarms(alarmIds, processor, action);

        // Then
        assertNotNull(result);
        assertEquals(2, result.totalCount());
        assertEquals(2, result.successCount());
        assertEquals(0, result.failureCount());
        
        verify(alarmRepository).findByIds(alarmIds);
        verify(alarmProcessingService).batchProcess(eq(alarms), eq(processor), any());
        verify(alarmRepository).saveAll(alarms);
    }

    @Test
    @DisplayName("批量处理告警 - 告警ID列表为空")
    void batchProcessAlarms_whenAlarmIdsIsEmpty_thenReturnEmptyResult() {
        // Given
        List<AlarmId> alarmIds = Arrays.asList();
        Integer processor = 1001;
        AlarmCommandService.BatchAction action = AlarmCommandService.BatchAction.START_PROCESSING;

        // When
        AlarmCommandService.BatchProcessResult result = 
            alarmCommandService.batchProcessAlarms(alarmIds, processor, action);

        // Then
        assertNotNull(result);
        assertEquals(0, result.totalCount());
        assertEquals(0, result.successCount());
        assertEquals(0, result.failureCount());
        
        verify(alarmRepository, never()).findByIds(any());
        verify(alarmProcessingService, never()).batchProcess(any(), any(), any());
        verify(alarmRepository, never()).saveAll(any());
    }

    @Test
    @DisplayName("更新告警状态 - 成功场景")
    void updateAlarmStatus_whenValidAlarm_thenSuccess() {
        // Given
        AlarmId alarmId = AlarmId.of("test-alarm-id");
        AlarmStatusEnum newStatus = AlarmStatusEnum.COMPLETED;
        Integer updatedBy = 1002;
        
        Alarm alarm = createTestAlarm();
        when(alarmRepository.findById(alarmId)).thenReturn(Optional.of(alarm));
        when(alarmRepository.save(any(Alarm.class))).thenReturn(alarm);

        // When
        boolean result = alarmCommandService.updateAlarmStatus(alarmId, newStatus, updatedBy);

        // Then
        assertTrue(result);
        assertEquals(newStatus, alarm.getAlarmStatus());
        assertEquals(updatedBy, alarm.getUpdatedBy());
        
        verify(alarmRepository).findById(alarmId);
        verify(alarmRepository).save(alarm);
    }

    @Test
    @DisplayName("删除告警 - 成功场景")
    void deleteAlarm_whenValidAlarm_thenSuccess() {
        // Given
        AlarmId alarmId = AlarmId.of("test-alarm-id");
        Alarm alarm = createTestAlarm();
        
        when(alarmRepository.findById(alarmId)).thenReturn(Optional.of(alarm));

        // When
        boolean result = alarmCommandService.deleteAlarm(alarmId);

        // Then
        assertTrue(result);
        verify(alarmRepository).findById(alarmId);
        verify(alarmRepository).delete(alarm);
    }

    @Test
    @DisplayName("删除告警 - 告警不存在")
    void deleteAlarm_whenAlarmNotFound_thenReturnFalse() {
        // Given
        AlarmId alarmId = AlarmId.of("non-existent-id");
        
        when(alarmRepository.findById(alarmId)).thenReturn(Optional.empty());

        // When
        boolean result = alarmCommandService.deleteAlarm(alarmId);

        // Then
        assertFalse(result);
        verify(alarmRepository).findById(alarmId);
        verify(alarmRepository, never()).delete(any());
    }

    @Test
    @DisplayName("BatchProcessResult - 创建和访问")
    void batchProcessResult_whenCreated_thenAccessCorrectly() {
        // Given
        int totalCount = 10;
        int successCount = 8;
        int failureCount = 2;
        List<String> failureReasons = Arrays.asList("告警1处理失败", "告警2处理失败");

        // When
        AlarmCommandService.BatchProcessResult result = 
            new AlarmCommandService.BatchProcessResult(totalCount, successCount, failureCount, failureReasons);

        // Then
        assertEquals(totalCount, result.totalCount());
        assertEquals(successCount, result.successCount());
        assertEquals(failureCount, result.failureCount());
        assertEquals(failureReasons, result.failureReasons());
        assertFalse(result.isAllSuccess());
        assertTrue(result.hasFailures());
    }

    // ==================== 辅助方法 ====================

    private Alarm createTestAlarm() {
        return Alarm.create(
            "测试告警",
            "TEST_ATTACK",
            3,
            new Date(),
            1001
        );
    }
}
