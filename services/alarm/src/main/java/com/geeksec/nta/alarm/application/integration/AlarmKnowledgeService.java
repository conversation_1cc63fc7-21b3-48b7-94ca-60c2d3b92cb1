package com.geeksec.nta.alarm.application.integration;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 告警知识库服务接口
 * 负责告警知识库的管理和查询
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmKnowledgeService {
    
    /**
     * 初始化告警知识库
     */
    void initKnowledgeBase();
    
    /**
     * 获取所有告警知识库条目
     * 
     * @return 知识库列表
     */
    List<AlarmKnowledge> getAllKnowledgeList();
    
    /**
     * 根据告警类型获取知识库信息
     * 
     * @param alarmType 告警类型
     * @return 知识库信息
     */
    AlarmKnowledge getKnowledgeByType(String alarmType);
    
    /**
     * 根据知识库ID获取详细信息
     * 
     * @param knowledgeId 知识库ID
     * @return 知识库详情
     */
    AlarmKnowledgeDetail getKnowledgeDetail(Long knowledgeId);
    
    /**
     * 搜索知识库
     * 
     * @param keyword 关键词
     * @param category 分类
     * @return 搜索结果
     */
    List<AlarmKnowledge> searchKnowledge(String keyword, String category);
    
    /**
     * 获取攻击链知识库
     * 
     * @param attackChainId 攻击链ID
     * @return 攻击链知识
     */
    AttackChainKnowledge getAttackChainKnowledge(String attackChainId);
    
    /**
     * 获取威胁情报关联信息
     * 
     * @param alarmType 告警类型
     * @return 威胁情报信息
     */
    List<ThreatIntelligence> getThreatIntelligence(String alarmType);
    
    /**
     * 更新知识库使用统计
     * 
     * @param knowledgeId 知识库ID
     * @param usageType 使用类型
     */
    void updateKnowledgeUsage(Long knowledgeId, String usageType);

    // ==================== 内部类定义 ====================

    /**
     * 告警知识库
     */
    record AlarmKnowledge(
        Long id,
        String name,
        String type,
        String category,
        String description,
        Integer severity,
        String mitigation,
        List<String> tags,
        LocalDateTime createdAt,
        LocalDateTime updatedAt,
        boolean isActive
    ) {}

    /**
     * 告警知识库详情
     */
    record AlarmKnowledgeDetail(
        Long id,
        String name,
        String type,
        String category,
        String description,
        Integer severity,
        String detailedDescription,
        String attackVector,
        String impact,
        String mitigation,
        String prevention,
        List<String> references,
        List<String> tags,
        Map<String, Object> metadata,
        LocalDateTime createdAt,
        LocalDateTime updatedAt,
        boolean isActive
    ) {}

    /**
     * 攻击链知识库
     */
    record AttackChainKnowledge(
        String chainId,
        String name,
        String description,
        List<AttackStep> steps,
        String tacticsAndTechniques,
        String mitreAttackId,
        List<String> indicators,
        String severity,
        LocalDateTime createdAt
    ) {}

    /**
     * 攻击步骤
     */
    record AttackStep(
        int sequence,
        String name,
        String description,
        String technique,
        List<String> indicators,
        String mitigation
    ) {}

    /**
     * 威胁情报
     */
    record ThreatIntelligence(
        String id,
        String type,
        String value,
        String source,
        String confidence,
        String severity,
        String description,
        Map<String, Object> attributes,
        LocalDateTime firstSeen,
        LocalDateTime lastSeen,
        boolean isActive
    ) {}

    /**
     * 知识库分类
     */
    enum KnowledgeCategory {
        MALWARE("恶意软件"),
        NETWORK_ATTACK("网络攻击"),
        WEB_ATTACK("Web攻击"),
        VULNERABILITY("漏洞利用"),
        RECONNAISSANCE("侦察行为"),
        LATERAL_MOVEMENT("横向移动"),
        PERSISTENCE("持久化"),
        PRIVILEGE_ESCALATION("权限提升"),
        DEFENSE_EVASION("防御规避"),
        CREDENTIAL_ACCESS("凭据访问"),
        DISCOVERY("发现"),
        COLLECTION("收集"),
        COMMAND_AND_CONTROL("命令与控制"),
        EXFILTRATION("数据窃取"),
        IMPACT("影响");

        private final String description;

        KnowledgeCategory(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 使用类型
     */
    enum UsageType {
        ALARM_GENERATION("告警生成"),
        MANUAL_QUERY("手动查询"),
        CORRELATION_ANALYSIS("关联分析"),
        THREAT_HUNTING("威胁狩猎");

        private final String description;

        UsageType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
