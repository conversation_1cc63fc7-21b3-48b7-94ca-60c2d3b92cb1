package com.geeksec.nta.alarm.interfaces.dto.response;

import com.geeksec.common.enums.AlarmHandlingStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 告警响应对象
 * 用于告警的通用响应，包含告警的基础信息和扩展字段
 * 适用于单个告警查询、列表查询等场景
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "告警响应")
public class AlarmResponse extends AlarmBaseResponse {

    private static final long serialVersionUID = 1L;

    // 基础字段已在父类 AlarmBaseResponse 中定义
    
    @Schema(description = "告警标题")
    private String title;

    @Schema(description = "告警来源")
    private String sourceType;

    @Schema(description = "告警原因代码")
    private String reasonCode;

    @Schema(description = "告警原因描述")
    private String reasonMessage;

    @Schema(description = "处理人ID")
    private String handlerId;

    @Schema(description = "处理人姓名")
    private String handlerName;

    @Schema(description = "处理时间")
    private LocalDateTime handleTime;

    @Schema(description = "告警评分")
    private Double score;

    @Schema(description = "是否已读")
    @Builder.Default
    private Boolean isRead = false;

    /**
     * 是否已处理
     */
    public Boolean isHandled() {
        return status != null && status == AlarmHandlingStatus.HANDLED;
    }

    /**
     * 获取威胁等级颜色
     */
    public String getThreatLevelColor() {
        if (threatLevel == null) {
            return "#666666";
        }
        return switch (threatLevel) {
            case CRITICAL -> "#ff4d4f";
            case HIGH -> "#ff7a45";
            case MEDIUM -> "#ffa940";
            case LOW -> "#52c41a";
            case NONE -> "#1890ff";
        };
    }

    /**
     * 获取状态颜色
     */
    public String getStatusColor() {
        if (status == null) {
            return "#666666";
        }
        return switch (status) {
            case UNCONFIRMED -> "#1890ff";
            case CONFIRMED -> "#ffa940";
            case HANDLED -> "#52c41a";
            case FALSE_POSITIVE -> "#d9d9d9";
        };
    }
}
