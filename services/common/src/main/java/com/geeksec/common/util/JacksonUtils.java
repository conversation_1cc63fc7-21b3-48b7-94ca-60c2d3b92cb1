package com.geeksec.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * Jackson JSON 工具类
 */
@Slf4j
public class JacksonUtils {
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    
    /**
     * 将对象转换为 JSON 字符串
     * 替代 JSON.toJSONString()
     */
    public static String toJSONString(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("对象转换为JSON字符串失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 将 JSON 字符串解析为 Map
     * 替代 JSON.parseObject()
     */
    public static Map<String, Object> parseObject(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonStr, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            log.error("JSON字符串解析为对象失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 将 JSON 字符串解析为指定类型的对象
     */
    public static <T> T parseObject(String jsonStr, Class<T> clazz) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonStr, clazz);
        } catch (JsonProcessingException e) {
            log.error("JSON字符串解析为{}类型失败: {}", clazz.getSimpleName(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 将 JSON 字符串解析为 List
     * 替代 JSON.parseArray()
     */
    public static <T> List<T> parseArray(String jsonStr, Class<T> clazz) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonStr, 
                OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (JsonProcessingException e) {
            log.error("JSON字符串解析为{}列表失败: {}", clazz.getSimpleName(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 将对象转换为 Map
     */
    public static Map<String, Object> objectToMap(Object obj) {
        if (obj == null) {
            return null;
        }
        return OBJECT_MAPPER.convertValue(obj, new TypeReference<Map<String, Object>>() {});
    }
    
    /**
     * 将 Map 转换为指定类型的对象
     */
    public static <T> T mapToObject(Map<String, Object> map, Class<T> clazz) {
        if (map == null) {
            return null;
        }
        return OBJECT_MAPPER.convertValue(map, clazz);
    }
    
    /**
     * 获取 JsonNode 对象，用于复杂的 JSON 操作
     */
    public static JsonNode parseTree(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readTree(jsonStr);
        } catch (JsonProcessingException e) {
            log.error("JSON字符串解析为JsonNode失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 将 JSON 字符串解析为指定类型的对象（支持 TypeReference）
     */
    public static <T> T parseObject(String jsonStr, TypeReference<T> typeReference) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonStr, typeReference);
        } catch (JsonProcessingException e) {
            log.error("JSON字符串解析为指定类型失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 创建空的 ObjectNode
     */
    public static ObjectNode createObjectNode() {
        return OBJECT_MAPPER.createObjectNode();
    }
    
    /**
     * 创建空的 ArrayNode
     */
    public static ArrayNode createArrayNode() {
        return OBJECT_MAPPER.createArrayNode();
    }
    
    /**
     * 将对象转换为 JsonNode
     */
    public static JsonNode valueToTree(Object obj) {
        return OBJECT_MAPPER.valueToTree(obj);
    }
    
    /**
     * 获取 ObjectMapper 实例，用于高级操作
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }
}