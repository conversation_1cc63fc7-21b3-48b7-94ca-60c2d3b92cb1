package com.geeksec.common.utils.io;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件工具类
 * 
 * 提供文件和目录操作的工具方法，基于Apache Commons IO库
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public final class FileUtils {
    
    private FileUtils() {
        // 工具类，禁止实例化
    }
    
    /**
     * 在目录中递归查找指定扩展名的文件
     * 
     * @param inputDirs 输入目录列表
     * @param extensions 文件扩展名列表（如 .pcap, .cap, .pcapng）
     * @return 匹配的文件路径列表
     */
    public static List<String> findFilesByExtensions(List<String> inputDirs, List<String> extensions) {
        List<String> matchedFiles = new ArrayList<>();
        
        for (String inputPath : inputDirs) {
            Path path = Paths.get(inputPath);
            
            if (Files.isRegularFile(path)) {
                // 如果是文件，检查是否匹配扩展名
                if (hasMatchingExtension(path, extensions)) {
                    matchedFiles.add(inputPath);
                }
            } else if (Files.isDirectory(path)) {
                // 如果是目录，递归查找文件
                matchedFiles.addAll(findFilesInDirectory(path, extensions));
            } else {
                log.warn("路径不存在或不是文件/目录: {}", inputPath);
            }
        }
        
        log.info("找到匹配文件数量: {}", matchedFiles.size());
        return matchedFiles;
    }
    
    /**
     * 在目录中递归查找指定扩展名的文件
     * 
     * @param directory 目录路径
     * @param extensions 文件扩展名列表
     * @return 匹配的文件路径列表
     */
    private static List<String> findFilesInDirectory(Path directory, List<String> extensions) {
        List<String> matchedFiles = new ArrayList<>();
        
        try (Stream<Path> paths = Files.walk(directory)) {
            paths.filter(Files::isRegularFile)
                 .filter(path -> hasMatchingExtension(path, extensions))
                 .forEach(path -> matchedFiles.add(path.toString()));
        } catch (IOException e) {
            log.error("遍历目录失败: {}", directory, e);
        }
        
        return matchedFiles;
    }
    
    /**
     * 检查文件是否匹配指定的扩展名
     * 
     * @param file 文件路径
     * @param extensions 扩展名列表
     * @return 是否匹配
     */
    private static boolean hasMatchingExtension(Path file, List<String> extensions) {
        String fileName = file.getFileName().toString().toLowerCase();
        return extensions.stream().anyMatch(fileName::endsWith);
    }
    
    /**
     * 写入文本文件（使用Apache Commons IO）
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     */
    public static void writeFile(Path filePath, String content) {
        try {
            File file = filePath.toFile();
            org.apache.commons.io.FileUtils.forceMkdirParent(file);
            org.apache.commons.io.FileUtils.writeStringToFile(file, content, StandardCharsets.UTF_8);
            log.debug("写入文件: {}", filePath);
        } catch (IOException e) {
            log.error("写入文件失败: filePath={}", filePath, e);
            throw new RuntimeException("写入文件失败", e);
        }
    }
    
    /**
     * 写入文本文件（使用Apache Commons IO）
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     * @return 是否写入成功
     */
    public static boolean writeFile(String filePath, String content) {
        if (StringUtils.isBlank(filePath)) {
            log.warn("文件路径不能为空");
            return false;
        }
        
        try {
            File file = new File(filePath);
            org.apache.commons.io.FileUtils.forceMkdirParent(file);
            org.apache.commons.io.FileUtils.writeStringToFile(file, content, StandardCharsets.UTF_8);
            log.debug("文件写入成功: {}", filePath);
            return true;
        } catch (IOException e) {
            log.error("文件写入失败: {}", filePath, e);
            return false;
        }
    }
    
    /**
     * 写入Base64编码的文件
     * 
     * @param filePath 文件路径
     * @param base64Content Base64编码的内容
     */
    public static void writeBase64File(Path filePath, String base64Content) {
        try {
            Files.createDirectories(filePath.getParent());
            byte[] decodedBytes = Base64.getDecoder().decode(base64Content);
            Files.write(filePath, decodedBytes);
            log.debug("写入Base64文件: {}", filePath);
        } catch (Exception e) {
            log.error("写入Base64文件失败: filePath={}", filePath, e);
            throw new RuntimeException("写入Base64文件失败", e);
        }
    }
    
    /**
     * 读取文件内容（使用Apache Commons IO）
     * 
     * @param filePath 文件路径
     * @return 文件内容
     */
    public static String readFile(Path filePath) {
        try {
            File file = filePath.toFile();
            return org.apache.commons.io.FileUtils.readFileToString(file, StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("读取文件失败: filePath={}", filePath, e);
            throw new RuntimeException("读取文件失败", e);
        }
    }
    
    /**
     * 读取文件内容（使用Apache Commons IO）
     * 
     * @param filePath 文件路径
     * @return 文件内容，读取失败返回null
     */
    public static String readFile(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            log.warn("文件路径不能为空");
            return null;
        }
        
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                log.warn("文件不存在: {}", filePath);
                return null;
            }
            
            String content = org.apache.commons.io.FileUtils.readFileToString(file, StandardCharsets.UTF_8);
            log.debug("文件读取成功: {}", filePath);
            return content;
        } catch (IOException e) {
            log.error("文件读取失败: {}", filePath, e);
            return null;
        }
    }
    
    /**
     * 复制文件（使用Apache Commons IO）
     * 
     * @param source 源文件路径
     * @param target 目标文件路径
     */
    public static void copyFile(Path source, Path target) {
        try {
            File sourceFile = source.toFile();
            File targetFile = target.toFile();
            org.apache.commons.io.FileUtils.copyFile(sourceFile, targetFile);
            log.debug("复制文件: {} -> {}", source, target);
        } catch (IOException e) {
            log.error("复制文件失败: source={}, target={}", source, target, e);
            throw new RuntimeException("复制文件失败", e);
        }
    }
    
    /**
     * 创建目录
     * 
     * @param directory 目录路径
     */
    public static void createDirectories(Path directory) {
        try {
            Files.createDirectories(directory);
            log.debug("创建目录: {}", directory);
        } catch (IOException e) {
            log.error("创建目录失败: directory={}", directory, e);
            throw new RuntimeException("创建目录失败", e);
        }
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 是否存在
     */
    public static boolean exists(Path filePath) {
        return Files.exists(filePath);
    }
    
    /**
     * 删除文件或目录（使用Apache Commons IO）
     * 
     * @param path 文件或目录路径
     * @return 是否删除成功
     */
    public static boolean delete(Path path) {
        try {
            File file = path.toFile();
            if (file.isDirectory()) {
                org.apache.commons.io.FileUtils.deleteDirectory(file);
            } else {
                org.apache.commons.io.FileUtils.delete(file);
            }
            log.debug("删除成功: {}", path);
            return true;
        } catch (IOException e) {
            log.error("删除失败: {}", path, e);
            return false;
        }
    }
    
    /**
     * 删除文件
     * 
     * @param filePath 文件路径
     */
    public static void deleteFile(Path filePath) {
        try {
            Files.deleteIfExists(filePath);
            log.debug("删除文件: {}", filePath);
        } catch (IOException e) {
            log.error("删除文件失败: filePath={}", filePath, e);
            throw new RuntimeException("删除文件失败", e);
        }
    }
    
    /**
     * 获取文件大小
     * 
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    public static long getFileSize(Path filePath) {
        try {
            return Files.size(filePath);
        } catch (IOException e) {
            log.error("获取文件大小失败: filePath={}", filePath, e);
            return 0;
        }
    }
    
    /**
     * 递归复制目录（使用Apache Commons IO）
     * 
     * @param sourceDir 源目录
     * @param targetDir 目标目录
     */
    public static void copyDirectory(Path sourceDir, Path targetDir) {
        try {
            File sourceDirFile = sourceDir.toFile();
            File targetDirFile = targetDir.toFile();
            
            if (!sourceDirFile.exists()) {
                log.warn("源目录不存在: {}", sourceDir);
                return;
            }
            
            org.apache.commons.io.FileUtils.copyDirectory(sourceDirFile, targetDirFile);
            log.debug("复制目录: {} -> {}", sourceDir, targetDir);
        } catch (IOException e) {
            log.error("复制目录失败: sourceDir={}, targetDir={}", sourceDir, targetDir, e);
            throw new RuntimeException("复制目录失败", e);
        }
    }
    
    /**
     * 检查路径是否为目录
     * 
     * @param path 路径
     * @return 是否为目录
     */
    public static boolean isDirectory(Path path) {
        return Files.isDirectory(path);
    }
    
    /**
     * 检查路径是否为文件
     * 
     * @param path 路径
     * @return 是否为文件
     */
    public static boolean isRegularFile(Path path) {
        return Files.isRegularFile(path);
    }
    
    // 注意：简单的文件操作请直接使用 Apache Commons IO (org.apache.commons.io.FileUtils)
    // 这里只保留有特定业务逻辑的文件处理方法
}