package com.geeksec.common.util;

import com.geeksec.common.entity.PageResultVo;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockHttpServletRequest;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工具类重构测试
 * 验证新的工具类功能是否正常工作
 */
class UtilsRefactoringTest {

    @Test
    void testValidationUtils() {
        // 测试字符串验证
        assertTrue(ValidationUtils.isEmpty(null));
        assertTrue(ValidationUtils.isEmpty(""));
        assertTrue(ValidationUtils.isEmpty("   "));
        assertFalse(ValidationUtils.isEmpty("test"));
        
        assertTrue(ValidationUtils.isNotEmpty("test"));
        assertFalse(ValidationUtils.isNotEmpty(null));
        assertFalse(ValidationUtils.isNotEmpty(""));
        
        // 测试邮箱验证
        assertTrue(ValidationUtils.isValidEmail("<EMAIL>"));
        assertFalse(ValidationUtils.isValidEmail("invalid-email"));
        assertFalse(ValidationUtils.isValidEmail(null));
        
        // 测试数值范围验证
        assertTrue(ValidationUtils.isInRange(5, 1, 10));
        assertFalse(ValidationUtils.isInRange(15, 1, 10));
        assertTrue(ValidationUtils.isInRange(5L, 1L, 10L));
        
        // 测试必填字段验证
        Map<String, Object> data = new HashMap<>();
        data.put("name", "test");
        data.put("email", "<EMAIL>");
        
        assertDoesNotThrow(() -> ValidationUtils.validateRequiredFields(data, "name,email"));
        
        data.put("phone", "");
        assertThrows(RuntimeException.class, () -> ValidationUtils.validateRequiredFields(data, "name,email,phone"));
    }
    
    @Test
    void testTypeConversionUtils() {
        // 测试字符串转整数
        assertEquals(123, TypeConversionUtils.convertStringToInt("123", 0));
        assertEquals(0, TypeConversionUtils.convertStringToInt("invalid", 0));
        assertEquals(0, TypeConversionUtils.convertStringToInt(null, 0));
        
        // 测试字符串转长整数
        assertEquals(123L, TypeConversionUtils.convertStringToLong("123", 0L));
        assertEquals(0L, TypeConversionUtils.convertStringToLong("invalid", 0L));
        
        // 测试字符串转双精度
        assertEquals(123.45, TypeConversionUtils.convertStringToDouble("123.45", 0.0), 0.001);
        assertEquals(0.0, TypeConversionUtils.convertStringToDouble("invalid", 0.0), 0.001);
        
        // 测试字符串转布尔值
        assertTrue(TypeConversionUtils.convertStringToBoolean("true", false));
        assertFalse(TypeConversionUtils.convertStringToBoolean("false", true));
        assertFalse(TypeConversionUtils.convertStringToBoolean("invalid", false));
        
        // 测试对象转换（使用字符串转换方法）
        assertEquals(123, TypeConversionUtils.convertStringToInt("123", 0));
        assertEquals(0, TypeConversionUtils.convertStringToInt("invalid", 0));
        assertEquals(123L, TypeConversionUtils.convertStringToLong("123", 0L));
    }
    
    @Test
    void testPageResultVo() {
        // 测试总页数计算
        assertEquals(3, PageResultVo.calculateTotalPages(10, 25));
        assertEquals(3, PageResultVo.calculateTotalPages(10, 30));
        assertEquals(1, PageResultVo.calculateTotalPages(10, 0));
        
        // 测试分页参数填充
        Map<String, Object> params = new HashMap<>();
        params.put("pageNum", 2);
        params.put("pageRow", 10);
        
        PageResultVo.fillPageParam(params, 20);
        assertEquals(10, params.get("offSet"));
        assertEquals(10, params.get("pageRow"));
        assertEquals(2, params.get("pageNum"));
        assertFalse(params.containsKey("pageSize"));
        
        // 测试分页结果创建
        List<String> data = Arrays.asList("item1", "item2", "item3");
        PageResultVo<String> pageResult = PageResultVo.of(data, 25, 1, 10);
        
        assertNotNull(pageResult);
        assertEquals(data, pageResult.getRecords());
        assertEquals(25, pageResult.getTotal());
        assertEquals(1, pageResult.getCurrentPage());
        assertEquals(10, pageResult.getPageSize());
        assertEquals(3, pageResult.getTotalPages());
        assertTrue(pageResult.isHasNext());
        assertFalse(pageResult.isHasPrevious());
        
        // 测试内存分页
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
        List<Integer> page1 = PageResultVo.pageList(numbers, 1, 3);
        assertEquals(Arrays.asList(1, 2, 3), page1);
        
        List<Integer> page2 = PageResultVo.pageList(numbers, 2, 3);
        assertEquals(Arrays.asList(4, 5, 6), page2);
    }
    
    @Test
    void testHttpRequestUtils() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setParameter("name", "test");
        request.setParameter("age", "25");
        request.setParameter("tags", "tag1", "tag2");
        
        // 测试请求参数转换
        Map<String, Object> params = HttpRequestUtils.requestToMap(request);
        assertEquals("test", params.get("name"));
        assertEquals("25", params.get("age"));
        assertEquals("tag1,tag2", params.get("tags"));
        
        // 测试带验证的请求参数转换
        assertDoesNotThrow(() -> HttpRequestUtils.requestToMapAndValidate(request, "name,age"));
        assertThrows(RuntimeException.class, () -> HttpRequestUtils.requestToMapAndValidate(request, "name,age,phone"));
        
        // 测试客户端IP获取
        request.setRemoteAddr("***********");
        assertEquals("***********", HttpRequestUtils.getClientIp(request));
        
        request.addHeader("X-Forwarded-For", "********");
        assertEquals("********", HttpRequestUtils.getClientIp(request));
        
        // 测试完整URL构建
        request.setRequestURI("/api/test");
        request.setQueryString("param=value");
        String fullUrl = HttpRequestUtils.getFullRequestUrl(request);
        assertTrue(fullUrl.contains("/api/test"));
        assertTrue(fullUrl.contains("param=value"));
    }
    
    @Test
    void testJsonResponseUtils() {
        // 测试成功响应
        Map<String, Object> successResponse = JsonResponseUtils.success();
        assertEquals(0, successResponse.get("err"));
        assertEquals("成功", successResponse.get("msg"));
        
        Map<String, Object> successWithData = JsonResponseUtils.success("test data");
        assertEquals("test data", successWithData.get("data"));
        
        // 测试错误响应
        Map<String, Object> errorResponse = JsonResponseUtils.error("错误信息");
        assertEquals(40000, errorResponse.get("err"));
        assertEquals("错误信息", errorResponse.get("msg"));
        
        Map<String, Object> errorWithCode = JsonResponseUtils.error(400, "参数错误");
        assertEquals(400, errorWithCode.get("err"));
        assertEquals("参数错误", errorWithCode.get("msg"));
        
        // 测试JSON解析
        String jsonStr = "{\"name\":\"test\",\"age\":25}";
        Map<String, Object> parsed = JsonResponseUtils.parseToMap(jsonStr);
        assertEquals("test", parsed.get("name"));
        assertEquals(25, parsed.get("age"));
        
        // 测试对象转JSON
        String jsonString = JsonResponseUtils.toJsonString(successResponse);
        assertNotNull(jsonString);
        assertTrue(jsonString.contains("成功"));
    }
    
    // NtaUtils 已被移除，相关功能已迁移到其他工具类中
}