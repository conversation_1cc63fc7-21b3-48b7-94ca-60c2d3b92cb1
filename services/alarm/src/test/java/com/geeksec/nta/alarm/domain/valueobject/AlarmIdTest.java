package com.geeksec.nta.alarm.domain.valueobject;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 告警ID值对象测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@DisplayName("告警ID值对象测试")
class AlarmIdTest {

    @Test
    @DisplayName("创建告警ID - 成功")
    void createAlarmId_Success() {
        // Given
        String value = "alarm-123";
        
        // When
        AlarmId alarmId = new AlarmId(value);
        
        // Then
        assertNotNull(alarmId);
        assertEquals(value, alarmId.getValue());
    }

    @Test
    @DisplayName("创建告警ID - 值为空应抛出异常")
    void createAlarmId_EmptyValue_ShouldThrowException() {
        // Given
        String value = "";
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            new AlarmId(value);
        });
    }

    @Test
    @DisplayName("创建告警ID - 值为null应抛出异常")
    void createAlarmId_NullValue_ShouldThrowException() {
        // Given
        String value = null;
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            new AlarmId(value);
        });
    }

    @Test
    @DisplayName("告警ID相等性测试 - 相同值应相等")
    void alarmIdEquality_SameValue_ShouldBeEqual() {
        // Given
        String value = "alarm-123";
        AlarmId alarmId1 = new AlarmId(value);
        AlarmId alarmId2 = new AlarmId(value);
        
        // When & Then
        assertEquals(alarmId1, alarmId2);
        assertEquals(alarmId1.hashCode(), alarmId2.hashCode());
    }

    @Test
    @DisplayName("告警ID相等性测试 - 不同值应不相等")
    void alarmIdEquality_DifferentValue_ShouldNotBeEqual() {
        // Given
        AlarmId alarmId1 = new AlarmId("alarm-123");
        AlarmId alarmId2 = new AlarmId("alarm-456");
        
        // When & Then
        assertNotEquals(alarmId1, alarmId2);
        assertNotEquals(alarmId1.hashCode(), alarmId2.hashCode());
    }

    @Test
    @DisplayName("告警ID toString测试")
    void alarmIdToString_ShouldReturnValue() {
        // Given
        String value = "alarm-123";
        AlarmId alarmId = new AlarmId(value);
        
        // When
        String result = alarmId.toString();
        
        // Then
        assertTrue(result.contains(value));
    }
}
