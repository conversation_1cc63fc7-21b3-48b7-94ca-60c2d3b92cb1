package com.geeksec.admin.application;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.geeksec.admin.domain.service.SystemManagementDomainService;
import com.geeksec.admin.domain.model.DiskManagement;
import com.geeksec.admin.domain.model.SystemInfo;
import com.geeksec.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 系统管理应用服务
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemManagementApplicationService {
    
    private final SystemManagementDomainService systemManagementDomainService;
    
    /**
     * 关闭主机操作
     * 
     * @return 执行结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ObjectNode shutdown() {
        log.info("执行系统关机操作");
        
        ObjectNode response = JacksonUtils.createObjectNode();
        try {
            boolean success = systemManagementDomainService.shutdownSystem(getCurrentUser());
            
            if (success) {
                response.put("result_code", "1");
                response.put("result_desc", "系统关机命令已发送");
                response.put("shutdown_time", System.currentTimeMillis() + 60000); // 1分钟后关机
            } else {
                response.put("result_code", "0");
                response.put("result_desc", "系统关机命令发送失败");
            }
        } catch (Exception e) {
            log.error("系统关机操作失败", e);
            response.put("result_code", "0");
            response.put("result_desc", "系统关机操作失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 重启主机操作
     * 
     * @return 执行结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ObjectNode reboot() {
        log.info("执行系统重启操作");
        
        ObjectNode response = JacksonUtils.createObjectNode();
        try {
            boolean success = systemManagementDomainService.rebootSystem(getCurrentUser());
            
            if (success) {
                response.put("result_code", "1");
                response.put("result_desc", "系统重启命令已发送");
                response.put("reboot_time", System.currentTimeMillis() + 60000); // 1分钟后重启
            } else {
                response.put("result_code", "0");
                response.put("result_desc", "系统重启命令发送失败");
            }
        } catch (Exception e) {
            log.error("系统重启操作失败", e);
            response.put("result_code", "0");
            response.put("result_desc", "系统重启操作失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 获取系统信息
     * 
     * @return 系统信息
     */
    public ObjectNode getSystemInfo() {
        log.debug("获取系统信息");
        
        ObjectNode response = JacksonUtils.createObjectNode();
        try {
            SystemInfo systemInfo = systemManagementDomainService.getSystemInfo();
            
            response.put("result_code", "1");
            response.put("result_desc", "获取系统信息成功");
            response.put("hostname", systemInfo.getHostname());
            response.put("os_info", systemInfo.getOsInfo());
            response.put("system_time", systemInfo.getSystemTime());
            response.put("uptime_seconds", systemInfo.getUptimeSeconds());
            response.put("uptime_days", systemInfo.getUptimeDays());
            response.put("start_time", systemInfo.getStartTime().toString());
            response.put("status", systemInfo.getStatus().getDescription());
            response.put("last_update_time", systemInfo.getLastUpdateTime().toString());
            
            // CPU信息
            if (systemInfo.getCpuInfo() != null) {
                ObjectNode cpuInfo = JacksonUtils.createObjectNode();
                cpuInfo.put("model", systemInfo.getCpuInfo().getModel());
                cpuInfo.put("cores", systemInfo.getCpuInfo().getCores());
                cpuInfo.put("frequency_mhz", systemInfo.getCpuInfo().getFrequencyMHz());
                cpuInfo.put("usage_percentage", systemInfo.getCpuInfo().getUsagePercentage());
                cpuInfo.put("temperature_celsius", systemInfo.getCpuInfo().getTemperatureCelsius());
                response.put("cpu_info", cpuInfo);
            }
            
            // 内存信息
            if (systemInfo.getMemoryInfo() != null) {
                ObjectNode memoryInfo = JacksonUtils.createObjectNode();
                memoryInfo.put("total_gb", systemInfo.getMemoryInfo().getTotalGB());
                memoryInfo.put("used_gb", systemInfo.getMemoryInfo().getUsedGB());
                memoryInfo.put("available_gb", systemInfo.getMemoryInfo().getAvailableGB());
                memoryInfo.put("usage_percentage", systemInfo.getMemoryInfo().getUsagePercentage());
                response.put("memory_info", memoryInfo);
            }
            
            // 磁盘信息
            if (systemInfo.getDiskInfo() != null) {
                ObjectNode diskInfo = JacksonUtils.createObjectNode();
                diskInfo.put("total_gb", systemInfo.getDiskInfo().getTotalGB());
                diskInfo.put("used_gb", systemInfo.getDiskInfo().getUsedGB());
                diskInfo.put("available_gb", systemInfo.getDiskInfo().getAvailableGB());
                diskInfo.put("usage_percentage", systemInfo.getDiskInfo().getUsagePercentage());
                response.put("disk_info", diskInfo);
            }
            
        } catch (Exception e) {
            log.error("获取系统信息失败", e);
            response.put("result_code", "0");
            response.put("result_desc", "获取系统信息失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 获取RAID卡磁盘信息
     * 
     * @return RAID信息
     */
    public ObjectNode getRaidInfo() {
        log.debug("获取RAID卡磁盘信息");
        
        ObjectNode response = JacksonUtils.createObjectNode();
        try {
            List<DiskManagement> raidDisks = systemManagementDomainService.getRaidInfo();
            
            response.put("result_code", "1");
            response.put("result_desc", "获取RAID信息成功");
            response.put("raid_count", raidDisks.size());
            
            // 转换RAID磁盘信息
            ObjectNode raidData = JacksonUtils.createObjectNode();
            for (DiskManagement disk : raidDisks) {
                ObjectNode diskInfo = JacksonUtils.createObjectNode();
                diskInfo.put("device_path", disk.getDevicePath());
                diskInfo.put("disk_type", disk.getDiskType().getDescription());
                diskInfo.put("status", disk.getStatus().getDescription());
                diskInfo.put("capacity_gb", disk.getCapacityGB());
                diskInfo.put("used_gb", disk.getUsedGB());
                diskInfo.put("available_gb", disk.getAvailableGB());
                diskInfo.put("usage_percentage", disk.getUsagePercentage());
                diskInfo.put("health_status", disk.getHealthStatus().getDescription());
                
                if (disk.getRaidInfo() != null) {
                    ObjectNode raidInfo = JacksonUtils.createObjectNode();
                    raidInfo.put("raid_level", disk.getRaidInfo().getRaidLevel());
                    raidInfo.put("raid_status", disk.getRaidInfo().getRaidStatus());
                    raidInfo.set("member_disks", JacksonUtils.valueToTree(disk.getRaidInfo().getMemberDisks()));
                    raidInfo.set("hot_spare_disks", JacksonUtils.valueToTree(disk.getRaidInfo().getHotSpareDisks()));
                    raidInfo.put("rebuild_progress", disk.getRaidInfo().getRebuildProgress());
                    raidInfo.put("is_rebuilding", disk.getRaidInfo().getIsRebuilding());
                    diskInfo.put("raid_info", raidInfo);
                }
                
                raidData.put(disk.getDiskId(), diskInfo);
            }
            response.put("raid_disks", raidData);
            
        } catch (Exception e) {
            log.error("获取RAID信息失败", e);
            response.put("result_code", "0");
            response.put("result_desc", "获取RAID信息失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 获取可管理的磁盘列表
     * 
     * @return 磁盘列表
     */
    public ObjectNode getManageableDisks() {
        log.debug("获取可管理的磁盘列表");
        
        ObjectNode response = JacksonUtils.createObjectNode();
        try {
            List<DiskManagement> disks = systemManagementDomainService.getDiskManagements();
            
            response.put("result_code", "1");
            response.put("result_desc", "获取磁盘列表成功");
            response.put("disk_count", disks.size());
            
            ObjectNode diskData = JacksonUtils.createObjectNode();
            for (DiskManagement disk : disks) {
                ObjectNode diskInfo = JacksonUtils.createObjectNode();
                diskInfo.put("device_path", disk.getDevicePath());
                diskInfo.put("disk_type", disk.getDiskType().getDescription());
                diskInfo.put("status", disk.getStatus().getDescription());
                diskInfo.put("file_system_type", disk.getFileSystemType());
                diskInfo.put("mount_point", disk.getMountPoint());
                diskInfo.put("capacity_gb", disk.getCapacityGB());
                diskInfo.put("used_gb", disk.getUsedGB());
                diskInfo.put("available_gb", disk.getAvailableGB());
                diskInfo.put("usage_percentage", disk.getUsagePercentage());
                diskInfo.put("health_status", disk.getHealthStatus().getDescription());
                diskInfo.put("is_online", disk.isOnline());
                diskInfo.put("is_healthy", disk.isHealthy());
                diskInfo.put("last_check_time", disk.getLastCheckTime().toString());
                
                diskData.put(disk.getDiskId(), diskInfo);
            }
            response.put("disks", diskData);
            
        } catch (Exception e) {
            log.error("获取磁盘列表失败", e);
            response.put("result_code", "0");
            response.put("result_desc", "获取磁盘列表失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 磁盘重组操作
     * 
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ObjectNode rebuildDisk() {
        log.info("执行磁盘重组操作");
        
        ObjectNode response = JacksonUtils.createObjectNode();
        try {
            // 获取RAID磁盘并执行重建
            List<DiskManagement> raidDisks = systemManagementDomainService.getRaidInfo();
            
            if (raidDisks.isEmpty()) {
                response.put("result_code", "0");
                response.put("result_desc", "未找到RAID磁盘");
                return response;
            }
            
            // 选择第一个RAID磁盘进行重建（实际应该根据具体需求选择）
            DiskManagement raidDisk = raidDisks.get(0);
            boolean success = systemManagementDomainService.rebuildRaid(raidDisk.getDevicePath(), getCurrentUser());
            
            if (success) {
                response.put("result_code", "1");
                response.put("result_desc", "磁盘重组操作已启动");
                response.put("device_path", raidDisk.getDevicePath());
                response.put("start_time", System.currentTimeMillis());
            } else {
                response.put("result_code", "0");
                response.put("result_desc", "磁盘重组操作启动失败");
            }
            
        } catch (Exception e) {
            log.error("磁盘重组操作失败", e);
            response.put("result_code", "0");
            response.put("result_desc", "磁盘重组操作失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 准备挂载磁盘
     * 
     * @return 操作结果
     */
    public ObjectNode prepareMount() {
        log.info("准备挂载磁盘");
        
        ObjectNode response = JacksonUtils.createObjectNode();
        try {
            // 检查可挂载的磁盘
            List<DiskManagement> disks = systemManagementDomainService.getDiskManagements();
            long unmountedCount = disks.stream()
                    .filter(disk -> disk.getMountPoint() == null || disk.getMountPoint().isEmpty())
                    .count();
            
            response.put("result_code", "1");
            response.put("result_desc", "磁盘挂载准备完成");
            response.put("total_disks", disks.size());
            response.put("unmounted_disks", unmountedCount);
            response.put("ready_for_mount", unmountedCount > 0);
            
        } catch (Exception e) {
            log.error("准备挂载磁盘失败", e);
            response.put("result_code", "0");
            response.put("result_desc", "准备挂载磁盘失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 挂载数据磁盘
     * 
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ObjectNode mountDataDisk() {
        log.info("挂载数据磁盘");
        
        ObjectNode response = JacksonUtils.createObjectNode();
        try {
            // 查找未挂载的磁盘
            List<DiskManagement> disks = systemManagementDomainService.getDiskManagements();
            DiskManagement unmountedDisk = disks.stream()
                    .filter(disk -> disk.getMountPoint() == null || disk.getMountPoint().isEmpty())
                    .filter(disk -> disk.getStatus() == DiskManagement.DiskStatus.ONLINE)
                    .findFirst()
                    .orElse(null);
            
            if (unmountedDisk == null) {
                response.put("result_code", "0");
                response.put("result_desc", "未找到可挂载的磁盘");
                return response;
            }
            
            // 执行挂载操作
            String mountPoint = "/data"; // 默认挂载点
            boolean success = systemManagementDomainService.mountDisk(
                    unmountedDisk.getDevicePath(), 
                    mountPoint, 
                    getCurrentUser()
            );
            
            if (success) {
                response.put("result_code", "1");
                response.put("result_desc", "数据磁盘挂载成功");
                response.put("device_path", unmountedDisk.getDevicePath());
                response.put("mount_point", mountPoint);
            } else {
                response.put("result_code", "0");
                response.put("result_desc", "数据磁盘挂载失败");
            }
            
        } catch (Exception e) {
            log.error("挂载数据磁盘失败", e);
            response.put("result_code", "0");
            response.put("result_desc", "挂载数据磁盘失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 格式化磁盘
     * 
     * @param devicePath 设备路径
     * @param fileSystem 文件系统类型
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ObjectNode formatDisk(String devicePath, String fileSystem) {
        log.info("格式化磁盘, 设备路径: {}, 文件系统: {}", devicePath, fileSystem);
        
        ObjectNode response = JacksonUtils.createObjectNode();
        try {
            boolean success = systemManagementDomainService.formatDisk(devicePath, fileSystem, getCurrentUser());
            
            if (success) {
                response.put("result_code", "1");
                response.put("result_desc", "磁盘格式化操作已启动");
                response.put("device_path", devicePath);
                response.put("file_system", fileSystem);
                response.put("start_time", System.currentTimeMillis());
            } else {
                response.put("result_code", "0");
                response.put("result_desc", "磁盘格式化操作启动失败");
            }
            
        } catch (Exception e) {
            log.error("格式化磁盘失败", e);
            response.put("result_code", "0");
            response.put("result_desc", "格式化磁盘失败: " + e.getMessage());
        }
        
        return response;
    }
    
    private String getCurrentUser() {
        // 从安全上下文获取当前用户
        // 这里简化处理，实际应该从Spring Security或其他认证框架获取
        return "system";
    }
}
