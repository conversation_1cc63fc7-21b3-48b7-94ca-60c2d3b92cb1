package com.geeksec.nta.alarm.domain.event;

import com.geeksec.nta.alarm.domain.valueobject.AlarmStatusEnum;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 告警处理完成事件
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
@ToString
public final class AlarmProcessedEvent implements DomainEvent {
    
    /**
     * 告警ID
     */
    private final String alarmId;
    
    /**
     * 处理者
     */
    private final String processor;
    
    /**
     * 处理结果
     */
    private final String processResult;
    
    /**
     * 最终状态
     */
    private final AlarmStatusEnum finalStatus;
    
    /**
     * 处理时长（分钟）
     */
    private final long processingDurationMinutes;
    
    /**
     * 事件发生时间
     */
    private final LocalDateTime eventTime;

    public AlarmProcessedEvent(String alarmId, String processor, String processResult, 
                             AlarmStatusEnum finalStatus, long processingDurationMinutes) {
        this.alarmId = Objects.requireNonNull(alarmId, "告警ID不能为空");
        this.processor = Objects.requireNonNull(processor, "处理者不能为空");
        this.processResult = processResult;
        this.finalStatus = Objects.requireNonNull(finalStatus, "最终状态不能为空");
        this.processingDurationMinutes = processingDurationMinutes;
        this.eventTime = LocalDateTime.now();
    }

    @Override
    public String getEventType() {
        return "AlarmProcessed";
    }

    @Override
    public String getAggregateId() {
        return alarmId;
    }

    @Override
    public LocalDateTime getEventTime() {
        return eventTime;
    }
}
