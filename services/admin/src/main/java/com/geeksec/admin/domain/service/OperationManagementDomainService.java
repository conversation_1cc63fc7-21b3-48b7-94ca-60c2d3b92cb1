package com.geeksec.admin.domain.service;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.geeksec.admin.domain.model.NetworkConfig;

/**
 * 运维管理领域服务接口 - 统一的运维管理领域服务
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface OperationManagementDomainService {
    
    // ==================== 网络管理相关 ====================
    
    /**
     * 修改IP配置
     *
     * @param config 网络配置
     * @return 操作结果
     */
    ObjectNode modifyIpConfig(NetworkConfig config);
    
    /**
     * 设置NTP服务器
     *
     * @param ntpServer NTP服务器地址
     * @return 操作结果
     */
    ObjectNode setNtpServer(String ntpServer);
    
    /**
     * 获取网络配置信息
     *
     * @return 网络配置
     */
    NetworkConfig getNetworkConfig();
    
    /**
     * 获取网络设备信息
     *
     * @param deviceName 设备名称
     * @return 设备信息
     */
    ObjectNode getNetworkDeviceInfo(String deviceName);
    
    /**
     * 获取所有网络接口信息
     *
     * @return 网络接口列表
     */
    ObjectNode getAllNetworkInterfaces();
    
    // ==================== 动态库检测相关 ====================
    
    /**
     * 检测动态库文件
     *
     * @param ruleId 规则ID
     * @return 检测结果
     */
    ObjectNode checkSoFiles(Integer ruleId);
    
    /**
     * 检测Docker动态库文件
     *
     * @param path 路径
     * @return 检测结果
     */
    ObjectNode checkDockerSoFiles(String path);
    
    /**
     * 检测系统动态库依赖
     *
     * @param libraryPath 库文件路径
     * @return 检测结果
     */
    ObjectNode checkLibraryDependencies(String libraryPath);
    
    /**
     * 获取系统已安装的动态库列表
     *
     * @return 动态库列表
     */
    ObjectNode getInstalledLibraries();
    
    // ==================== 系统操作相关 ====================
    
    /**
     * 关闭主机
     *
     * @return 执行状态
     */
    ObjectNode shutdown();
    
    /**
     * 重启主机
     *
     * @return 执行状态
     */
    ObjectNode reboot();
    
    /**
     * 修改密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 执行状态
     */
    ObjectNode changePassword(String userName, String password);
    
    /**
     * 获取磁盘使用情况
     *
     * @return 磁盘信息
     */
    ObjectNode getDiskInfoData();
    
    /**
     * 获取系统负载信息
     *
     * @return 系统负载
     */
    ObjectNode getSystemLoad();
    
    /**
     * 获取进程信息
     *
     * @return 进程列表
     */
    ObjectNode getProcessInfo();
    
    /**
     * 获取服务状态
     *
     * @param serviceName 服务名称
     * @return 服务状态
     */
    ObjectNode getServiceStatus(String serviceName);
    
    /**
     * 启动服务
     *
     * @param serviceName 服务名称
     * @return 操作结果
     */
    ObjectNode startService(String serviceName);
    
    /**
     * 停止服务
     *
     * @param serviceName 服务名称
     * @return 操作结果
     */
    ObjectNode stopService(String serviceName);
    
    /**
     * 重启服务
     *
     * @param serviceName 服务名称
     * @return 操作结果
     */
    ObjectNode restartService(String serviceName);
    
    /**
     * 获取系统日志
     *
     * @param logType 日志类型
     * @param lines 行数
     * @return 日志内容
     */
    ObjectNode getSystemLogs(String logType, Integer lines);
    
    /**
     * 清理系统缓存
     *
     * @return 清理结果
     */
    ObjectNode clearSystemCache();
    
    /**
     * 获取系统性能指标
     *
     * @return 性能指标
     */
    ObjectNode getPerformanceMetrics();
}
