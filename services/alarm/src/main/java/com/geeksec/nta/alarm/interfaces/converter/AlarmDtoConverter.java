package com.geeksec.nta.alarm.interfaces.converter;

import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmDetailResponse;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警转换器
 *
 * 负责告警领域对象与各种响应DTO之间的转换
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Component
public class AlarmDtoConverter {

    /**
     * 将告警聚合根转换为标准响应DTO
     */
    public AlarmResponse toResponse(Alarm alarm) {
        if (alarm == null) {
            return null;
        }

        return AlarmResponse.builder()
                .alarmId(alarm.getId())
                .alarmName(alarm.getAlarmName())
                .alarmType(alarm.getAlarmType())
                .threatLevel(alarm.getThreatLevel())
                .status(alarm.getAlarmStatus())
                .alarmTime(alarm.getAlarmTime())
                .createdAt(alarm.getCreatedAt())
                .updatedAt(alarm.getUpdatedAt())
                .createdBy(alarm.getCreatedBy())
                .updatedBy(alarm.getUpdatedBy())
                .description(alarm.getDescription())
                .attackerIp(getFirstAttackerIp(alarm))
                .victimIp(getFirstVictimIp(alarm))
                .attackerPort(getFirstAttackerPort(alarm))
                .victimPort(getFirstVictimPort(alarm))
                .protocol(alarm.getProtocol())
                .processRemark(alarm.getProcessRemark())
                .closeReason(alarm.getCloseReason())
                .extendedProperties(alarm.getExtendedProperties())
                .build();
    }



    /**
     * 将告警聚合根转换为详情响应DTO
     */
    public AlarmDetailResponse toDetailResponse(Alarm alarm) {
        if (alarm == null) {
            return null;
        }

        return AlarmDetailResponse.builder()
                .alarmId(alarm.getId().getValue())
                .alarmName(alarm.getAlarmName())
                .alarmType(alarm.getAlarmType())
                .threatLevel(alarm.getThreatLevel())
                .status(alarm.getAlarmStatus())
                .alarmTime(alarm.getAlarmTime())
                .createdAt(alarm.getCreatedAt())
                .updatedAt(alarm.getUpdatedAt())
                .createdBy(alarm.getCreatedBy())
                .updatedBy(alarm.getUpdatedBy())
                .description(alarm.getDescription())
                .attackerIp(alarm.getAttackerIp())
                .victimIp(alarm.getVictimIp())
                .attackerPort(alarm.getAttackerPort())
                .victimPort(alarm.getVictimPort())
                .protocol(alarm.getProtocol())
                .processRemark(alarm.getProcessRemark())
                .closeReason(alarm.getCloseReason())
                .extendedProperties(alarm.getExtendedProperties())
                // TODO: 添加详情特有字段的转换逻辑
                .build();
    }

    /**
     * 批量转换为响应DTO
     */
    public List<AlarmResponse> toResponses(List<Alarm> alarms) {
        if (alarms == null || alarms.isEmpty()) {
            return List.of();
        }
        return alarms.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取第一个攻击者IP
     */
    private String getFirstAttackerIp(Alarm alarm) {
        List<String> attackerIps = alarm.getAttackerIps();
        return attackerIps.isEmpty() ? null : attackerIps.get(0);
    }

    /**
     * 获取第一个受害者IP
     */
    private String getFirstVictimIp(Alarm alarm) {
        List<String> victimIps = alarm.getVictimIps();
        return victimIps.isEmpty() ? null : victimIps.get(0);
    }

    /**
     * 获取第一个攻击者端口
     */
    private Integer getFirstAttackerPort(Alarm alarm) {
        if (alarm.getAttackers() == null || alarm.getAttackers().isEmpty()) {
            return null;
        }
        return alarm.getAttackers().get(0).getPort();
    }

    /**
     * 获取第一个受害者端口
     */
    private Integer getFirstVictimPort(Alarm alarm) {
        if (alarm.getVictims() == null || alarm.getVictims().isEmpty()) {
            return null;
        }
        return alarm.getVictims().get(0).getPort();
    }
}
