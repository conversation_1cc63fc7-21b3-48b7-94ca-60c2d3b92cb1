package com.geeksec.nta.alarm.application.service;


import com.geeksec.nta.alarm.domain.aggregate.notification.NotificationResult;
import com.geeksec.nta.alarm.domain.repository.NotificationResultRepository;
import com.geeksec.nta.alarm.infrastructure.mapper.NotificationResultMapper;
import com.mybatisflex.core.paginate.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 通知结果查询服务实现
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationResultQueryServiceImpl implements NotificationResultQueryService {

    private final NotificationResultRepository notificationResultRepository;

    @Override
    public Optional<NotificationResult> findById(String id) {
        log.debug("根据ID查询通知结果: id={}", id);

        try {
            return notificationResultRepository.findById(id);
        } catch (Exception e) {
            log.error("根据ID查询通知结果失败: id={}", id, e);
            throw new RuntimeException("根据ID查询通知结果失败", e);
        }
    }

    @Override
    public Page<NotificationResult> findByPage(int pageNum, int pageSize,
                                             String subscriptionId, String alarmId,
                                             String channel, String status,
                                             LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("分页查询通知结果: pageNum={}, pageSize={}", pageNum, pageSize);

        try {
            return notificationResultRepository.findByPage(pageNum, pageSize,
                    subscriptionId, alarmId, channel, status, startTime, endTime);
        } catch (Exception e) {
            log.error("分页查询通知结果失败: pageNum={}, pageSize={}", pageNum, pageSize, e);
            throw new RuntimeException("分页查询通知结果失败", e);
        }
    }

    @Override
    public List<NotificationResult> findByAlarmId(String alarmId) {
        log.debug("根据告警ID查询通知结果: alarmId={}", alarmId);

        try {
            return notificationResultRepository.findByAlarmId(alarmId);
        } catch (Exception e) {
            log.error("根据告警ID查询通知结果失败: alarmId={}", alarmId, e);
            throw new RuntimeException("根据告警ID查询通知结果失败", e);
        }
    }

    @Override
    public List<NotificationResult> findBySubscriptionId(String subscriptionId, Integer limit) {
        log.debug("根据订阅ID查询通知结果: subscriptionId={}, limit={}", subscriptionId, limit);

        try {
            return notificationResultRepository.findBySubscriptionId(subscriptionId, limit);
        } catch (Exception e) {
            log.error("根据订阅ID查询通知结果失败: subscriptionId={}", subscriptionId, e);
            throw new RuntimeException("根据订阅ID查询通知结果失败", e);
        }
    }

    @Override
    public List<NotificationResult> findFailedNotifications(Integer maxRetryCount, Integer limit) {
        log.debug("查询失败的通知记录: maxRetryCount={}, limit={}", maxRetryCount, limit);

        try {
            return notificationResultRepository.findFailedNotifications(maxRetryCount, limit);
        } catch (Exception e) {
            log.error("查询失败的通知记录失败: maxRetryCount={}, limit={}", maxRetryCount, limit, e);
            throw new RuntimeException("查询失败的通知记录失败", e);
        }
    }

    @Override
    public NotificationResultMapper.NotificationStatistics getStatistics(String subscriptionId,
                                                                        LocalDateTime startTime,
                                                                        LocalDateTime endTime) {
        log.debug("查询通知统计: subscriptionId={}", subscriptionId);

        try {
            return notificationResultRepository.getStatistics(subscriptionId, startTime, endTime);
        } catch (Exception e) {
            log.error("查询通知统计失败: subscriptionId={}", subscriptionId, e);
            throw new RuntimeException("查询通知统计失败", e);
        }
    }

    @Override
    public List<NotificationResultMapper.DailySuccessRate> getDailySuccessRate(String subscriptionId, Integer days) {
        log.debug("查询每日成功率: subscriptionId={}, days={}", subscriptionId, days);

        try {
            return notificationResultRepository.getDailySuccessRate(subscriptionId, days);
        } catch (Exception e) {
            log.error("查询每日成功率失败: subscriptionId={}, days={}", subscriptionId, days, e);
            throw new RuntimeException("查询每日成功率失败", e);
        }
    }
}
