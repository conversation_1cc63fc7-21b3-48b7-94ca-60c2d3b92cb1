package com.geeksec.nta.alarm.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 告警攻击者值对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmAttacker {
    
    /**
     * 攻击者IP地址
     */
    private String ip;
    
    /**
     * 攻击者端口
     */
    private Integer port;
    
    /**
     * 攻击者主机名
     */
    private String hostname;
    
    /**
     * 攻击者MAC地址
     */
    private String macAddress;
    
    /**
     * 攻击者地理位置
     */
    private String location;
    
    /**
     * 攻击者组织信息
     */
    private String organization;
    
    /**
     * 攻击者国家
     */
    private String country;
    
    /**
     * 攻击者威胁情报标签
     */
    private String threatLabel;
    
    /**
     * 创建攻击者对象
     * 
     * @param ip IP地址
     * @return 攻击者对象
     */
    public static AlarmAttacker of(String ip) {
        Objects.requireNonNull(ip, "攻击者IP不能为空");
        AlarmAttacker attacker = new AlarmAttacker();
        attacker.ip = ip;
        return attacker;
    }
    
    /**
     * 创建攻击者对象
     * 
     * @param ip IP地址
     * @param port 端口
     * @return 攻击者对象
     */
    public static AlarmAttacker of(String ip, Integer port) {
        Objects.requireNonNull(ip, "攻击者IP不能为空");
        AlarmAttacker attacker = new AlarmAttacker();
        attacker.ip = ip;
        attacker.port = port;
        return attacker;
    }
    
    /**
     * 获取攻击者的完整地址
     * 
     * @return 完整地址
     */
    public String getFullAddress() {
        if (port != null && port > 0) {
            return ip + ":" + port;
        }
        return ip;
    }
    
    /**
     * 是否为外网地址
     * 
     * @return 是否为外网地址
     */
    public boolean isPublicAddress() {
        if (ip == null) {
            return false;
        }
        
        // 排除内网地址
        return !isPrivateAddress() && !isLoopbackAddress();
    }
    
    /**
     * 是否为内网地址
     * 
     * @return 是否为内网地址
     */
    public boolean isPrivateAddress() {
        if (ip == null) {
            return false;
        }
        
        return ip.startsWith("192.168.") || 
               ip.startsWith("10.") || 
               ip.startsWith("172.16.") ||
               ip.startsWith("172.17.") ||
               ip.startsWith("172.18.") ||
               ip.startsWith("172.19.") ||
               ip.startsWith("172.20.") ||
               ip.startsWith("172.21.") ||
               ip.startsWith("172.22.") ||
               ip.startsWith("172.23.") ||
               ip.startsWith("172.24.") ||
               ip.startsWith("172.25.") ||
               ip.startsWith("172.26.") ||
               ip.startsWith("172.27.") ||
               ip.startsWith("172.28.") ||
               ip.startsWith("172.29.") ||
               ip.startsWith("172.30.") ||
               ip.startsWith("172.31.");
    }
    
    /**
     * 是否为回环地址
     * 
     * @return 是否为回环地址
     */
    public boolean isLoopbackAddress() {
        return ip != null && ip.startsWith("127.");
    }
    
    /**
     * 是否为已知威胁
     * 
     * @return 是否为已知威胁
     */
    public boolean isKnownThreat() {
        return threatLabel != null && !threatLabel.trim().isEmpty();
    }
}
