# 告警管理模块架构变更说明

## 变更概述

基于架构设计分析，我们移除了告警服务模块中的创建告警功能，因为告警创建完全由Flink作业负责。这种设计符合流处理架构的最佳实践。

## 变更内容

### 1. 移除的组件

#### REST接口层
- 移除了 `AlarmController.createAlarm()` 方法
- 移除了 `@PostMapping` 创建告警的端点

#### DTO层
- 删除了 `AlarmCreateRequest.java` 文件

#### 应用服务层
- 移除了 `AlarmCommandService.createAlarm()` 接口方法
- 移除了 `AlarmCommandServiceImpl.createAlarm()` 实现方法

#### 领域层
- 移除了 `Alarm.create()` 静态工厂方法
- 移除了相关的领域事件发布逻辑

### 2. 保留的组件

#### 仓储层
- **保留** `AlarmRepository.save()` 方法，因为它同时用于创建和更新
- 保留所有查询、更新、删除相关方法

#### 其他功能
- 保留所有告警查询功能
- 保留所有告警更新功能（状态变更、处理等）
- 保留所有告警删除功能
- 保留告警统计和分析功能

## 架构设计原理

### 1. 职责分离
- **Flink作业**：负责告警创建、数据处理、流式计算
- **告警服务**：负责告警查询、业务逻辑、状态管理

### 2. CQRS模式
- **Command（写入）**：由Flink作业直接写入数据库
- **Query（查询）**：由告警服务提供API接口

### 3. 事件驱动架构
- Flink处理后将告警发送到Kafka
- 告警服务可以监听Kafka事件进行后续处理

## 数据流向

```
检测模块 → Kafka → Flink作业 → PostgreSQL
                      ↓
                   Kafka → 通知服务
                      ↓
                   告警服务API（查询/更新/删除）
```

## API变更

### 移除的端点
```http
POST /api/v1/alarms  # 创建告警（已移除）
```

### 保留的端点
```http
GET    /api/v1/alarms           # 查询告警列表
GET    /api/v1/alarms/{id}      # 查询告警详情
PUT    /api/v1/alarms/{id}      # 更新告警
DELETE /api/v1/alarms/{id}      # 删除告警
POST   /api/v1/alarms/batch     # 批量操作
GET    /api/v1/alarms/statistics # 告警统计
```

## 影响评估

### 正面影响
1. **性能提升**：流式处理直接写入，延迟更低
2. **架构清晰**：职责分离更明确
3. **扩展性好**：支持大规模实时数据处理
4. **一致性强**：符合事件驱动架构原则

### 注意事项
1. **测试更新**：需要更新相关的单元测试和集成测试
2. **文档同步**：需要更新API文档和用户手册
3. **监控调整**：需要调整监控指标和告警规则

## 后续工作

1. 清理测试代码中对创建告警方法的调用
2. 更新API文档和Swagger注解
3. 验证Flink作业的告警创建功能
4. 确保数据一致性监控机制

## 总结

这次架构变更使系统更符合微服务和流处理的最佳实践，提高了系统的性能和可维护性。告警创建由专门的流处理作业负责，而告警服务专注于业务逻辑和查询功能。
