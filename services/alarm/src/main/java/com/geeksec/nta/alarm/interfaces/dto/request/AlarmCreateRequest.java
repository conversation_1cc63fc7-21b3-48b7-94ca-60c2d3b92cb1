package com.geeksec.nta.alarm.interfaces.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.util.Date;

/**
 * 创建告警请求DTO
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Schema(description = "创建告警请求")
public class AlarmCreateRequest {

    @NotBlank(message = "告警名称不能为空")
    @Schema(description = "告警名称", example = "SQL注入攻击")
    private String alarmName;

    @NotBlank(message = "告警类型不能为空")
    @Schema(description = "告警类型", example = "WEB_ATTACK")
    private String alarmType;

    @NotNull(message = "威胁等级不能为空")
    @Min(value = 1, message = "威胁等级最小值为1")
    @Max(value = 4, message = "威胁等级最大值为4")
    @Schema(description = "威胁等级", example = "3")
    private Integer threatLevel;

    @NotNull(message = "告警时间不能为空")
    @Schema(description = "告警时间")
    private Date alarmTime;

    @NotNull(message = "创建者不能为空")
    @Schema(description = "创建者ID", example = "1001")
    private Integer createdBy;

    @Schema(description = "告警描述")
    private String description;

    @Schema(description = "攻击者IP")
    private String attackerIp;

    @Schema(description = "受害者IP")
    private String victimIp;

    @Schema(description = "攻击者端口")
    private Integer attackerPort;

    @Schema(description = "受害者端口")
    private Integer victimPort;

    @Schema(description = "协议类型")
    private String protocol;

    @Schema(description = "原始数据")
    private String rawData;
}
