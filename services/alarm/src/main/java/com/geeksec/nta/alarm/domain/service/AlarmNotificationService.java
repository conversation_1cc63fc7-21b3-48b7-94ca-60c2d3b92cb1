package com.geeksec.nta.alarm.domain.service;

import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.enums.AlarmStatusEnum;
import com.geeksec.nta.alarm.domain.valueobject.ThreatLevel;


import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 告警通知领域服务
 *
 * 负责告警的通知逻辑，包括通知规则判断、通知内容生成、通知发送等
 * 这是一个纯领域服务，不依赖任何框架
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class AlarmNotificationService {

    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 判断是否需要发送通知
     * 
     * @param alarm 告警
     * @param notificationRules 通知规则
     * @return 是否需要通知
     */
    public boolean shouldNotify(Alarm alarm, List<NotificationRule> notificationRules) {
        Objects.requireNonNull(alarm, "告警不能为空");
        Objects.requireNonNull(notificationRules, "通知规则不能为空");
        
        // 判断告警是否需要通知

        for (NotificationRule rule : notificationRules) {
            if (rule.matches(alarm)) {
                // 告警匹配通知规则
                return true;
            }
        }

        // 告警不匹配任何通知规则
        return false;
    }

    /**
     * 生成通知内容
     * 
     * @param alarm 告警
     * @param template 通知模板
     * @return 通知内容
     */
    public NotificationContent generateNotificationContent(Alarm alarm, NotificationTemplate template) {
        Objects.requireNonNull(alarm, "告警不能为空");
        Objects.requireNonNull(template, "通知模板不能为空");
        
        // 生成告警通知内容
        
        Map<String, String> variables = buildTemplateVariables(alarm);
        
        String title = replaceVariables(template.getTitleTemplate(), variables);
        String content = replaceVariables(template.getContentTemplate(), variables);
        
        return new NotificationContent(
            title,
            content,
            alarm.getThreatLevel(),
            LocalDateTime.now()
        );
    }

    /**
     * 构建模板变量
     */
    private Map<String, String> buildTemplateVariables(Alarm alarm) {
        Map<String, String> variables = new HashMap<>();
        
        variables.put("alarmId", alarm.getAlarmId().getValue());
        variables.put("alarmName", alarm.getAlarmName());
        variables.put("alarmType", alarm.getAlarmType());
        variables.put("threatLevel", alarm.getThreatLevel().getDescription());
        variables.put("status", alarm.getAlarmStatus().getDescription());
        variables.put("alarmTime", alarm.getAlarmTime().toInstant()
            .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime()
            .format(DATETIME_FORMATTER));
        
        // 攻击者信息
        List<String> attackerIps = alarm.getAttackerIps();
        variables.put("attackerCount", String.valueOf(attackerIps.size()));
        variables.put("attackerIps", String.join(", ", attackerIps));
        
        // 受害者信息
        List<String> victimIps = alarm.getVictimIps();
        variables.put("victimCount", String.valueOf(victimIps.size()));
        variables.put("victimIps", String.join(", ", victimIps));
        
        // 攻击链信息
        List<String> attackChains = alarm.getAttackChainNames();
        variables.put("attackChainCount", String.valueOf(attackChains.size()));
        variables.put("attackChains", String.join(", ", attackChains));
        
        return variables;
    }

    /**
     * 替换模板变量
     */
    private String replaceVariables(String template, Map<String, String> variables) {
        String result = template;
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            result = result.replace(placeholder, entry.getValue());
        }
        return result;
    }

    /**
     * 创建紧急通知
     * 
     * @param alarm 告警
     * @return 紧急通知内容
     */
    public NotificationContent createUrgentNotification(Alarm alarm) {
        Objects.requireNonNull(alarm, "告警不能为空");
        
        // 创建紧急通知
        
        String title = String.format("【紧急告警】%s - %s", 
            alarm.getThreatLevel().getDescription(), alarm.getAlarmName());
        
        StringBuilder content = new StringBuilder();
        content.append("检测到紧急安全告警，请立即处理！\n\n");
        content.append("告警ID: ").append(alarm.getAlarmId().getValue()).append("\n");
        content.append("告警名称: ").append(alarm.getAlarmName()).append("\n");
        content.append("告警类型: ").append(alarm.getAlarmType()).append("\n");
        content.append("威胁等级: ").append(alarm.getThreatLevel().getDescription()).append("\n");
        content.append("发生时间: ").append(alarm.getAlarmTime().toInstant()
            .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime()
            .format(DATETIME_FORMATTER)).append("\n");
        
        if (!alarm.getAttackerIps().isEmpty()) {
            content.append("攻击者IP: ").append(String.join(", ", alarm.getAttackerIps())).append("\n");
        }
        
        if (!alarm.getVictimIps().isEmpty()) {
            content.append("受害者IP: ").append(String.join(", ", alarm.getVictimIps())).append("\n");
        }
        
        content.append("\n请尽快登录系统查看详情并处理。");
        
        return new NotificationContent(
            title,
            content.toString(),
            alarm.getThreatLevel(),
            LocalDateTime.now()
        );
    }

    /**
     * 批量生成通知
     * 
     * @param alarms 告警列表
     * @param template 通知模板
     * @return 批量通知结果
     */
    public BatchNotificationResult generateBatchNotifications(List<Alarm> alarms, NotificationTemplate template) {
        Objects.requireNonNull(alarms, "告警列表不能为空");
        Objects.requireNonNull(template, "通知模板不能为空");
        
        // 批量生成通知
        
        List<NotificationContent> notifications = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;
        StringBuilder errorMessages = new StringBuilder();
        
        for (Alarm alarm : alarms) {
            try {
                NotificationContent content = generateNotificationContent(alarm, template);
                notifications.add(content);
                successCount++;
            } catch (Exception e) {
                failureCount++;
                errorMessages.append(String.format("告警 %s 生成通知失败: %s; ", 
                    alarm.getAlarmId().getValue(), e.getMessage()));
                // 记录生成失败
            }
        }
        
        // 批量生成通知完成
        
        return new BatchNotificationResult(
            notifications,
            successCount,
            failureCount,
            errorMessages.toString()
        );
    }

    // ==================== 内部类定义 ====================

    /**
     * 通知规则
     */
    public static class NotificationRule {
        private final String name;
        private final Set<ThreatLevel> threatLevels;
        private final Set<String> alarmTypes;
        private final Set<AlarmStatusEnum> statuses;

        public NotificationRule(String name, Set<ThreatLevel> threatLevels, 
                              Set<String> alarmTypes, Set<AlarmStatusEnum> statuses) {
            this.name = Objects.requireNonNull(name, "规则名称不能为空");
            this.threatLevels = threatLevels != null ? threatLevels : Set.of();
            this.alarmTypes = alarmTypes != null ? alarmTypes : Set.of();
            this.statuses = statuses != null ? statuses : Set.of();
        }

        public boolean matches(Alarm alarm) {
            // 检查威胁等级
            if (!threatLevels.isEmpty() && !threatLevels.contains(alarm.getThreatLevel())) {
                return false;
            }
            
            // 检查告警类型
            if (!alarmTypes.isEmpty() && !alarmTypes.contains(alarm.getAlarmType())) {
                return false;
            }
            
            // 检查状态
            if (!statuses.isEmpty() && !statuses.contains(alarm.getAlarmStatus())) {
                return false;
            }
            
            return true;
        }

        public String getName() { return name; }
    }

    /**
     * 通知模板
     */
    public static class NotificationTemplate {
        private final String name;
        private final String titleTemplate;
        private final String contentTemplate;

        public NotificationTemplate(String name, String titleTemplate, String contentTemplate) {
            this.name = Objects.requireNonNull(name, "模板名称不能为空");
            this.titleTemplate = Objects.requireNonNull(titleTemplate, "标题模板不能为空");
            this.contentTemplate = Objects.requireNonNull(contentTemplate, "内容模板不能为空");
        }

        public String getName() { return name; }
        public String getTitleTemplate() { return titleTemplate; }
        public String getContentTemplate() { return contentTemplate; }
    }

    /**
     * 通知内容
     */
    public record NotificationContent(
        String title,
        String content,
        ThreatLevel threatLevel,
        LocalDateTime createdAt
    ) {}

    /**
     * 批量通知结果
     */
    public record BatchNotificationResult(
        List<NotificationContent> notifications,
        int successCount,
        int failureCount,
        String errorMessages
    ) {
        public boolean isAllSuccess() {
            return failureCount == 0;
        }
        
        public int getTotalCount() {
            return successCount + failureCount;
        }
    }
}
