package com.geeksec.nta.alarm.application.service;

import com.geeksec.nta.alarm.application.command.CreateSuppressionCommand;
import com.geeksec.nta.alarm.application.command.DeleteSuppressionCommand;
import com.geeksec.nta.alarm.application.command.UpdateSuppressionCommand;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;

import java.time.Duration;
import java.util.List;

/**
 * 告警抑制命令应用服务
 * 负责处理所有抑制规则状态变更相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmSuppressionCommandService {
    
    /**
     * 创建抑制规则
     * 
     * @param command 创建命令
     * @return 抑制规则ID
     */
    SuppressionId createSuppression(CreateSuppressionCommand command);
    
    /**
     * 更新抑制规则
     * 
     * @param command 更新命令
     * @return 是否成功
     */
    boolean updateSuppression(UpdateSuppressionCommand command);
    
    /**
     * 删除抑制规则
     * 
     * @param command 删除命令
     * @return 是否成功
     */
    boolean deleteSuppression(DeleteSuppressionCommand command);
    
    /**
     * 批量创建抑制规则
     * 
     * @param commands 创建命令列表
     * @return 创建结果
     */
    BatchCreateResult batchCreateSuppressions(List<CreateSuppressionCommand> commands);
    
    /**
     * 批量删除抑制规则
     * 
     * @param commands 删除命令列表
     * @return 成功删除的数量
     */
    int batchDeleteSuppressions(List<DeleteSuppressionCommand> commands);
    
    /**
     * 根据条件批量删除抑制规则
     * 
     * @param victim 受害者IP（可选）
     * @param attacker 攻击者IP（可选）
     * @param label 告警标签（可选）
     * @param operator 操作人
     * @return 删除的数量
     */
    int deleteSuppressionsByCondition(String victim, String attacker, String label, String operator);
    
    /**
     * 启用抑制规则
     * 
     * @param suppressionId 抑制规则ID
     * @param operator 操作人
     * @return 是否成功
     */
    boolean enableSuppression(SuppressionId suppressionId, String operator);
    
    /**
     * 禁用抑制规则
     * 
     * @param suppressionId 抑制规则ID
     * @param operator 操作人
     * @return 是否成功
     */
    boolean disableSuppression(SuppressionId suppressionId, String operator);
    
    /**
     * 延长抑制规则有效期
     * 
     * @param suppressionId 抑制规则ID
     * @param duration 延长时间
     * @param operator 操作人
     * @return 是否成功
     */
    boolean extendSuppression(SuppressionId suppressionId, Duration duration, String operator);
    
    /**
     * 撤销抑制规则
     * 
     * @param suppressionId 抑制规则ID
     * @param reason 撤销原因
     * @param operator 操作人
     * @return 是否成功
     */
    boolean revokeSuppression(SuppressionId suppressionId, String reason, String operator);
    
    /**
     * 清理过期的抑制规则
     * 
     * @return 清理的数量
     */
    int cleanupExpiredSuppressions();
    
    /**
     * 重置抑制规则统计信息
     * 
     * @param suppressionId 抑制规则ID
     * @return 是否成功
     */
    boolean resetSuppressionStatistics(SuppressionId suppressionId);
    
    /**
     * 批量创建结果
     */
    record BatchCreateResult(
        int totalRequests,
        int successCount,
        int failureCount,
        List<String> createdIds,
        List<String> errors,
        String summary
    ) {}
}
