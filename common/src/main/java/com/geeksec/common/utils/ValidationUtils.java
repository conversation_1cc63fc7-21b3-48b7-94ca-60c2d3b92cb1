package com.geeksec.common.utils;

import java.util.Map;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;

import lombok.extern.slf4j.Slf4j;

/**
 * 验证工具类
 * 
 * 提供常用的数据验证功能，基于Apache Commons Lang3和Guava库
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public final class ValidationUtils {
    
    /**
     * 邮箱验证正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );
    
    private ValidationUtils() {
        // 工具类，禁止实例化
    }
    
    // 注意：简单的字符串空值检查请直接使用 StringUtils.isEmpty/isNotEmpty/isBlank/isNotBlank
    // 这里只保留有特定业务逻辑的验证方法
    
    /**
     * 检查数值是否在指定范围内（使用Apache Commons Lang3）
     * 
     * @param value 待检查的数值
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @return 如果数值在范围内，返回true
     */
    public static boolean isInRange(int value, int min, int max) {
        try {
            Validate.inclusiveBetween(min, max, value);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * 检查数值是否在指定范围内（使用Apache Commons Lang3）
     * 
     * @param value 待检查的数值
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @return 如果数值在范围内，返回true
     */
    public static boolean isInRange(long value, long min, long max) {
        try {
            Validate.inclusiveBetween(min, max, value);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * 检查数值是否在指定范围内（使用Apache Commons Lang3）
     * 
     * @param value 待检查的数值
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @return 如果数值在范围内，返回true
     */
    public static boolean isInRange(double value, double min, double max) {
        try {
            Validate.inclusiveBetween(min, max, value);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * 验证邮箱格式是否正确
     * 
     * @param email 邮箱地址
     * @return 如果邮箱格式正确则返回true
     */
    public static boolean isValidEmail(String email) {
        if (StringUtils.isEmpty(email)) {
            return false;
        }
        return EMAIL_PATTERN.matcher(email).matches();
    }
    
    /**
     * 验证Map中的必填字段
     * 
     * @param data 要验证的数据Map
     * @param requiredFields 必填字段数组
     * @throws IllegalArgumentException 如果有必填字段缺失
     */
    public static void validateRequiredFields(Map<String, Object> data, String... requiredFields) {
        if (data == null) {
            throw new IllegalArgumentException("数据不能为空");
        }
        
        for (String field : requiredFields) {
            Object value = data.get(field);
            if (value == null || (value instanceof String && StringUtils.isEmpty((String) value))) {
                throw new IllegalArgumentException("必填字段缺失: " + field);
            }
        }
    }
}