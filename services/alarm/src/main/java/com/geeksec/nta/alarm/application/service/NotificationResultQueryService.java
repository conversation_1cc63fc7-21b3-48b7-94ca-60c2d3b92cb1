package com.geeksec.nta.alarm.application.service;

import com.geeksec.nta.alarm.domain.aggregate.notification.NotificationResult;
import com.geeksec.nta.alarm.infrastructure.mapper.NotificationResultMapper;
import com.mybatisflex.core.paginate.Page;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 通知结果查询服务接口
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface NotificationResultQueryService {

    /**
     * 根据ID查询通知结果
     *
     * @param id 通知结果ID
     * @return 通知结果
     */
    Optional<NotificationResult> findById(String id);

    /**
     * 分页查询通知结果
     *
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param subscriptionId 订阅ID
     * @param alarmId 告警ID
     * @param channel 通知渠道
     * @param status 发送状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    Page<NotificationResult> findByPage(int pageNum, int pageSize,
                                      String subscriptionId, String alarmId,
                                      String channel, String status,
                                      LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据告警ID查询通知结果
     *
     * @param alarmId 告警ID
     * @return 通知结果列表
     */
    List<NotificationResult> findByAlarmId(String alarmId);

    /**
     * 根据订阅ID查询通知结果
     *
     * @param subscriptionId 订阅ID
     * @param limit 查询数量限制
     * @return 通知结果列表
     */
    List<NotificationResult> findBySubscriptionId(String subscriptionId, Integer limit);

    /**
     * 查询失败的通知记录
     *
     * @param maxRetryCount 最大重试次数
     * @param limit 查询数量限制
     * @return 失败的通知记录
     */
    List<NotificationResult> findFailedNotifications(Integer maxRetryCount, Integer limit);

    /**
     * 查询通知统计信息
     *
     * @param subscriptionId 订阅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    NotificationResultMapper.NotificationStatistics getStatistics(String subscriptionId,
                                                                 LocalDateTime startTime,
                                                                 LocalDateTime endTime);

    /**
     * 查询每日成功率
     *
     * @param subscriptionId 订阅ID
     * @param days 天数
     * @return 每日成功率列表
     */
    List<NotificationResultMapper.DailySuccessRate> getDailySuccessRate(String subscriptionId, Integer days);
}
