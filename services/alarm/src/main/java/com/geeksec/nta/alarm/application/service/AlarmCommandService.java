package com.geeksec.nta.alarm.application.service;

import java.util.List;

import com.geeksec.nta.alarm.application.command.DeleteAlarmCommand;
import com.geeksec.nta.alarm.application.command.UpdateAlarmStatusCommand;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.geeksec.nta.alarm.domain.valueobject.ThreatLevel;

/**
 * 告警命令应用服务
 * 负责处理告警状态变更和删除相关的业务逻辑
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmCommandService {

    /**
     * 提升威胁等级
     *
     * @param alarmId        告警ID
     * @param newThreatLevel 新威胁等级
     * @param updatedBy      更新者
     * @return 是否成功
     */
    boolean upgradeThreatLevel(AlarmId alarmId, ThreatLevel newThreatLevel, Integer updatedBy);

    /**
     * 更新告警状态
     *
     * @param command 更新命令
     * @return 是否成功
     */
    boolean updateAlarmStatus(UpdateAlarmStatusCommand command);

    /**
     * 批量更新告警状态
     *
     * @param commands 更新命令列表
     * @return 成功更新的数量
     */
    int batchUpdateAlarmStatus(List<UpdateAlarmStatusCommand> commands);

    /**
     * 批量管理告警状态
     *
     * @param alarmIds  告警ID列表
     * @param processor 操作者
     * @param action    批量操作类型（仅支持状态管理操作）
     * @return 处理结果
     */
    BatchProcessResult batchManageAlarms(List<AlarmId> alarmIds, Integer processor, BatchAction action);

    /**
     * 删除告警
     *
     * @param command 删除命令
     * @return 是否成功
     */
    boolean deleteAlarm(DeleteAlarmCommand command);

    /**
     * 批量删除告警
     *
     * @param commands 删除命令列表
     * @return 成功删除的数量
     */
    int batchDeleteAlarms(List<DeleteAlarmCommand> commands);

    /**
     * 删除所有告警
     *
     * @return 删除的数量
     */
    long deleteAllAlarms();

    /**
     * 归档过期告警
     *
     * @param daysToKeep 保留天数
     * @return 归档的数量
     */
    long archiveExpiredAlarms(int daysToKeep);

    // ==================== 内部类定义 ====================

    /**
     * 批量操作类型（仅支持状态管理操作）
     */
    enum BatchAction {
        MARK_FALSE_POSITIVE("标记误报"),
        IGNORE("忽略"),
        CLOSE("关闭告警");

        private final String description;

        BatchAction(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 批量处理结果
     */
    record BatchProcessResult(
            int totalCount,
            int successCount,
            int failureCount,
            List<String> errorMessages) {
        public boolean isAllSuccess() {
            return failureCount == 0;
        }
    }
}
