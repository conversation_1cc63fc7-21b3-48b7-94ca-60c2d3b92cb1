package com.geeksec.nta.alarm.domain.valueobject;

import lombok.Getter;

/**
 * 告警状态枚举
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum AlarmStatusEnum {

    /**
     * 新建 - 刚创建的告警
     */
    NEW(0, "新建", "刚创建的告警，等待处理"),

    /**
     * 处理中 - 正在处理的告警
     */
    PROCESSING(1, "处理中", "正在分析和处理的告警"),

    /**
     * 已处理 - 处理完成的告警
     */
    PROCESSED(2, "已处理", "已完成处理的告警"),

    /**
     * 误报 - 确认为误报的告警
     */
    FALSE_POSITIVE(3, "误报", "经确认为误报的告警"),

    /**
     * 忽略 - 选择忽略的告警
     */
    IGNORED(4, "忽略", "选择忽略的告警"),

    /**
     * 关闭 - 已关闭的告警
     */
    CLOSED(5, "关闭", "已关闭的告警");

    private final Integer code;
    private final String description;
    private final String detail;

    AlarmStatusEnum(Integer code, String description, String detail) {
        this.code = code;
        this.description = description;
        this.detail = detail;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举
     */
    public static AlarmStatusEnum fromCode(Integer code) {
        if (code == null) {
            return NEW;
        }
        
        for (AlarmStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("无效的告警状态码: " + code);
    }

    /**
     * 检查是否可以转换到目标状态
     *
     * @param target 目标状态
     * @return 是否可以转换
     */
    public boolean canTransitionTo(AlarmStatusEnum target) {
        if (target == null) {
            return false;
        }

        // 定义状态转换规则
        return switch (this) {
            case NEW -> target == PROCESSING || target == FALSE_POSITIVE || target == IGNORED;
            case PROCESSING -> target == PROCESSED || target == FALSE_POSITIVE || target == IGNORED;
            case PROCESSED -> target == CLOSED || target == FALSE_POSITIVE;
            case FALSE_POSITIVE -> target == PROCESSING; // 误报可以重新处理
            case IGNORED -> target == PROCESSING; // 忽略的可以重新处理
            case CLOSED -> false; // 已关闭的不能再转换
        };
    }

    /**
     * 是否为终态
     *
     * @return 是否为终态
     */
    public boolean isFinalState() {
        return this == CLOSED || this == FALSE_POSITIVE || this == IGNORED;
    }

    /**
     * 是否为活跃状态
     *
     * @return 是否为活跃状态
     */
    public boolean isActive() {
        return this == NEW || this == PROCESSING;
    }
}
