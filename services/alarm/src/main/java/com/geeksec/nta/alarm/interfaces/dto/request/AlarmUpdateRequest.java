package com.geeksec.nta.alarm.interfaces.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 更新告警请求DTO
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Schema(description = "更新告警请求")
public class AlarmUpdateRequest {

    @Schema(description = "告警名称", example = "SQL注入攻击")
    private String alarmName;

    @Schema(description = "告警类型", example = "WEB_ATTACK")
    private String alarmType;

    @Min(value = 1, message = "威胁等级最小值为1")
    @Max(value = 4, message = "威胁等级最大值为4")
    @Schema(description = "威胁等级", example = "3")
    private Integer threatLevel;

    @Schema(description = "告警状态", example = "1")
    private Integer status;

    @Schema(description = "更新者ID", example = "1002")
    private Integer updatedBy;

    @Schema(description = "告警描述")
    private String description;

    @Schema(description = "处理备注")
    private String processRemark;

    @Schema(description = "关闭原因")
    private String closeReason;
}
