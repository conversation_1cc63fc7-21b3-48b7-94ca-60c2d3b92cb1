package com.geeksec.nta.alarm.domain.service;

import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.enums.AlarmStatusEnum;
import com.geeksec.nta.alarm.domain.valueobject.ThreatLevel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AlarmProcessingService领域服务简单测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@DisplayName("AlarmProcessingService领域服务测试")
class AlarmProcessingServiceSimpleTest {

    private AlarmProcessingService alarmProcessingService;

    @BeforeEach
    void setUp() {
        alarmProcessingService = new AlarmProcessingService();
    }

    @Test
    @DisplayName("开始处理告警 - 成功场景")
    void startProcessing_whenValidAlarm_thenSuccess() {
        // Given
        Alarm alarm = createTestAlarm();
        Integer processor = 1001;

        // When
        assertDoesNotThrow(() -> alarmProcessingService.startProcessing(alarm, processor));

        // Then
        assertEquals(AlarmStatusEnum.PROCESSING, alarm.getAlarmStatus());
    }

    @Test
    @DisplayName("开始处理告警 - 告警为空")
    void startProcessing_whenAlarmIsNull_thenThrowException() {
        // Given
        Alarm alarm = null;
        Integer processor = 1001;

        // When & Then
        NullPointerException exception = assertThrows(
            NullPointerException.class,
            () -> alarmProcessingService.startProcessing(alarm, processor)
        );
        assertEquals("告警不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("开始处理告警 - 处理者为空")
    void startProcessing_whenProcessorIsNull_thenThrowException() {
        // Given
        Alarm alarm = createTestAlarm();
        Integer processor = null;

        // When & Then
        NullPointerException exception = assertThrows(
            NullPointerException.class,
            () -> alarmProcessingService.startProcessing(alarm, processor)
        );
        assertEquals("处理者不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("完成处理告警 - 成功场景")
    void completeProcessing_whenValidAlarm_thenSuccess() {
        // Given
        Alarm alarm = createTestAlarm();
        Integer processor = 1001;
        AlarmProcessingService.ProcessResult processResult = 
            new AlarmProcessingService.ProcessResult("已确认", "误报", true);

        // 先开始处理
        alarmProcessingService.startProcessing(alarm, processor);

        // When
        assertDoesNotThrow(() -> 
            alarmProcessingService.completeProcessing(alarm, processor, processResult)
        );

        // Then
        assertEquals(AlarmStatusEnum.COMPLETED, alarm.getAlarmStatus());
    }

    @Test
    @DisplayName("完成处理告警 - 处理结果为空")
    void completeProcessing_whenProcessResultIsNull_thenThrowException() {
        // Given
        Alarm alarm = createTestAlarm();
        Integer processor = 1001;
        AlarmProcessingService.ProcessResult processResult = null;

        // When & Then
        NullPointerException exception = assertThrows(
            NullPointerException.class,
            () -> alarmProcessingService.completeProcessing(alarm, processor, processResult)
        );
        assertEquals("处理结果不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("关闭告警 - 成功场景")
    void closeAlarm_whenValidAlarm_thenSuccess() {
        // Given
        Alarm alarm = createTestAlarm();
        Integer operator = 1001;
        String reason = "误报";

        // When
        assertDoesNotThrow(() -> alarmProcessingService.closeAlarm(alarm, operator, reason));

        // Then
        assertEquals(AlarmStatusEnum.CLOSED, alarm.getAlarmStatus());
    }

    @Test
    @DisplayName("关闭告警 - 操作者为空")
    void closeAlarm_whenOperatorIsNull_thenThrowException() {
        // Given
        Alarm alarm = createTestAlarm();
        Integer operator = null;
        String reason = "误报";

        // When & Then
        NullPointerException exception = assertThrows(
            NullPointerException.class,
            () -> alarmProcessingService.closeAlarm(alarm, operator, reason)
        );
        assertEquals("操作者不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("批量处理告警 - 成功场景")
    void batchProcess_whenValidAlarms_thenSuccess() {
        // Given
        List<Alarm> alarms = Arrays.asList(
            createTestAlarm(),
            createTestAlarm()
        );
        Integer processor = 1001;
        AlarmProcessingService.BatchAction batchAction = 
            AlarmProcessingService.BatchAction.START_PROCESSING;

        // When
        AlarmProcessingService.BatchProcessResult result = 
            alarmProcessingService.batchProcess(alarms, processor, batchAction);

        // Then
        assertNotNull(result);
        assertEquals(2, result.successCount());
        assertEquals(0, result.failureCount());
        assertTrue(result.isAllSuccess());
        
        // 验证所有告警状态都已更新
        alarms.forEach(alarm -> assertEquals(AlarmStatusEnum.PROCESSING, alarm.getAlarmStatus()));
    }

    @Test
    @DisplayName("批量处理告警 - 告警列表为空")
    void batchProcess_whenAlarmsIsNull_thenThrowException() {
        // Given
        List<Alarm> alarms = null;
        Integer processor = 1001;
        AlarmProcessingService.BatchAction batchAction = 
            AlarmProcessingService.BatchAction.START_PROCESSING;

        // When & Then
        NullPointerException exception = assertThrows(
            NullPointerException.class,
            () -> alarmProcessingService.batchProcess(alarms, processor, batchAction)
        );
        assertEquals("告警列表不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("评估威胁等级 - 成功场景")
    void assessThreatLevel_whenValidAlarm_thenReturnThreatLevel() {
        // Given
        Alarm alarm = createTestAlarm();

        // When
        ThreatLevel result = alarmProcessingService.assessThreatLevel(alarm);

        // Then
        assertNotNull(result);
        assertTrue(result.getLevel() >= 1 && result.getLevel() <= 4);
    }

    @Test
    @DisplayName("评估威胁等级 - 告警为空")
    void assessThreatLevel_whenAlarmIsNull_thenThrowException() {
        // Given
        Alarm alarm = null;

        // When & Then
        NullPointerException exception = assertThrows(
            NullPointerException.class,
            () -> alarmProcessingService.assessThreatLevel(alarm)
        );
        assertEquals("告警不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("ProcessResult - 创建和访问")
    void processResult_whenCreated_thenAccessCorrectly() {
        // Given
        String result = "已确认";
        String remark = "误报";
        boolean shouldClose = true;

        // When
        AlarmProcessingService.ProcessResult processResult = 
            new AlarmProcessingService.ProcessResult(result, remark, shouldClose);

        // Then
        assertEquals(result, processResult.getResult());
        assertEquals(remark, processResult.getRemark());
        assertTrue(processResult.shouldClose());
        assertTrue(processResult.hasRemark());
    }

    @Test
    @DisplayName("ProcessResult - 无备注")
    void processResult_whenNoRemark_thenHasRemarkReturnsFalse() {
        // Given
        AlarmProcessingService.ProcessResult processResult = 
            new AlarmProcessingService.ProcessResult("已确认", null, false);

        // When & Then
        assertFalse(processResult.hasRemark());
    }

    @Test
    @DisplayName("BatchProcessResult - 创建和访问")
    void batchProcessResult_whenCreated_thenAccessCorrectly() {
        // Given
        int successCount = 5;
        int failureCount = 2;
        String errorMessages = "部分告警处理失败";

        // When
        AlarmProcessingService.BatchProcessResult result = 
            new AlarmProcessingService.BatchProcessResult(successCount, failureCount, errorMessages);

        // Then
        assertEquals(successCount, result.successCount());
        assertEquals(failureCount, result.failureCount());
        assertEquals(errorMessages, result.errorMessages());
        assertEquals(7, result.getTotalCount());
        assertFalse(result.isAllSuccess());
    }

    @Test
    @DisplayName("BatchProcessResult - 全部成功")
    void batchProcessResult_whenAllSuccess_thenIsAllSuccessReturnsTrue() {
        // Given
        AlarmProcessingService.BatchProcessResult result = 
            new AlarmProcessingService.BatchProcessResult(5, 0, null);

        // When & Then
        assertTrue(result.isAllSuccess());
    }

    // ==================== 辅助方法 ====================

    private Alarm createTestAlarm() {
        return Alarm.create(
            "测试告警",
            "TEST_ATTACK",
            3,
            new Date(),
            1001
        );
    }
}
