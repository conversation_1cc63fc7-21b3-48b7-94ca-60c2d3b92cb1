package com.geeksec.nta.alarm.domain.repository;

import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.valueobject.AlarmStatusEnum;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.geeksec.nta.alarm.domain.valueobject.ThreatLevel;
import com.geeksec.common.entity.PageResultVo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 告警聚合根仓储接口
 * 定义告警聚合的持久化操作
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmRepository {
    
    /**
     * 保存告警
     * 
     * @param alarm 告警聚合根
     * @return 保存后的告警
     */
    Alarm save(Alarm alarm);
    
    /**
     * 根据ID查找告警
     * 
     * @param alarmId 告警ID
     * @return 告警聚合根
     */
    Optional<Alarm> findById(AlarmId alarmId);
    
    /**
     * 根据索引和ID查找告警
     * 
     * @param index ES索引
     * @param alarmId 告警ID
     * @return 告警聚合根
     */
    Optional<Alarm> findByIndexAndId(String index, String alarmId);
    
    /**
     * 删除告警
     * 
     * @param alarmId 告警ID
     * @return 是否删除成功
     */
    boolean deleteById(AlarmId alarmId);
    
    /**
     * 批量删除告警
     * 
     * @param alarmIds 告警ID列表
     * @return 删除的数量
     */
    int batchDelete(List<AlarmId> alarmIds);
    
    /**
     * 分页查询告警
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageResultVo<Alarm> findByPage(AlarmQuery query);
    
    /**
     * 统计告警数量
     * 
     * @param query 查询条件
     * @return 告警数量
     */
    long count(AlarmQuery query);
    
    /**
     * 检查告警是否存在
     * 
     * @param alarmId 告警ID
     * @return 是否存在
     */
    boolean existsById(AlarmId alarmId);
    
    /**
     * 删除所有告警
     *
     * @return 删除的数量
     */
    long deleteAll();

    /**
     * 根据状态查找告警
     *
     * @param status 告警状态
     * @return 告警列表
     */
    List<Alarm> findByStatus(AlarmStatusEnum status);

    /**
     * 根据威胁等级查找告警
     *
     * @param threatLevel 威胁等级
     * @return 告警列表
     */
    List<Alarm> findByThreatLevel(ThreatLevel threatLevel);

    /**
     * 根据告警类型查找告警
     *
     * @param alarmType 告警类型
     * @return 告警列表
     */
    List<Alarm> findByAlarmType(String alarmType);

    /**
     * 根据时间范围查找告警
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 告警列表
     */
    List<Alarm> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据攻击者IP查找告警
     *
     * @param attackerIp 攻击者IP
     * @return 告警列表
     */
    List<Alarm> findByAttackerIp(String attackerIp);

    /**
     * 根据受害者IP查找告警
     *
     * @param victimIp 受害者IP
     * @return 告警列表
     */
    List<Alarm> findByVictimIp(String victimIp);

    /**
     * 查找高威胁等级的未处理告警
     *
     * @return 告警列表
     */
    List<Alarm> findHighThreatUnprocessedAlarms();

    /**
     * 查找指定时间内的相关告警
     *
     * @param alarm 目标告警
     * @param timeRangeHours 时间范围（小时）
     * @return 相关告警列表
     */
    List<Alarm> findRelatedAlarms(Alarm alarm, int timeRangeHours);

    /**
     * 批量保存告警
     *
     * @param alarms 告警列表
     * @return 保存的数量
     */
    int batchSave(List<Alarm> alarms);

    /**
     * 批量更新告警状态
     *
     * @param alarmIds 告警ID列表
     * @param status 新状态
     * @param updatedBy 更新者
     * @return 更新的数量
     */
    int batchUpdateStatus(List<AlarmId> alarmIds, AlarmStatusEnum status, Integer updatedBy);

    /**
     * 告警查询条件
     */
    interface AlarmQuery {
        // 查询条件接口，具体实现在application层
    }
}
