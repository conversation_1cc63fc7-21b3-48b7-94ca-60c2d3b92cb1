package com.geeksec.nta.alarm.domain.aggregate.subscription;

import com.geeksec.nta.alarm.domain.valueobject.SubscriptionId;
import com.geeksec.nta.alarm.domain.valueobject.UserId;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 告警订阅聚合测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@DisplayName("告警订阅聚合测试")
class AlarmSubscriptionTest {

    @Test
    @DisplayName("创建告警订阅 - 成功")
    void createSubscription_Success() {
        // Given
        String subscriptionName = "测试订阅";
        UserId userId = new UserId(1L);
        String description = "测试订阅描述";
        
        // When
        AlarmSubscription subscription = AlarmSubscription.create(
            subscriptionName, userId, description
        );
        
        // Then
        assertNotNull(subscription);
        assertNotNull(subscription.getId());
        assertEquals(subscriptionName, subscription.getSubscriptionName());
        assertEquals(userId, subscription.getUserId());
        assertEquals(description, subscription.getDescription());
        assertTrue(subscription.isEnabled());
        assertNotNull(subscription.getCreateTime());
    }

    @Test
    @DisplayName("启用订阅 - 成功")
    void enableSubscription_Success() {
        // Given
        AlarmSubscription subscription = createTestSubscription();
        subscription.disable();
        
        // When
        subscription.enable();
        
        // Then
        assertTrue(subscription.isEnabled());
    }

    @Test
    @DisplayName("禁用订阅 - 成功")
    void disableSubscription_Success() {
        // Given
        AlarmSubscription subscription = createTestSubscription();
        
        // When
        subscription.disable();
        
        // Then
        assertFalse(subscription.isEnabled());
    }

    @Test
    @DisplayName("更新订阅信息 - 成功")
    void updateSubscription_Success() {
        // Given
        AlarmSubscription subscription = createTestSubscription();
        String newName = "新订阅名称";
        String newDescription = "新订阅描述";
        
        // When
        subscription.updateInfo(newName, newDescription);
        
        // Then
        assertEquals(newName, subscription.getSubscriptionName());
        assertEquals(newDescription, subscription.getDescription());
        assertNotNull(subscription.getUpdateTime());
    }

    @Test
    @DisplayName("创建订阅 - 名称为空应抛出异常")
    void createSubscription_EmptyName_ShouldThrowException() {
        // Given
        String subscriptionName = "";
        UserId userId = new UserId(1L);
        String description = "测试订阅描述";
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            AlarmSubscription.create(subscriptionName, userId, description);
        });
    }

    @Test
    @DisplayName("创建订阅 - 用户ID为空应抛出异常")
    void createSubscription_NullUserId_ShouldThrowException() {
        // Given
        String subscriptionName = "测试订阅";
        UserId userId = null;
        String description = "测试订阅描述";
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            AlarmSubscription.create(subscriptionName, userId, description);
        });
    }

    // ==================== 辅助方法 ====================

    private AlarmSubscription createTestSubscription() {
        return AlarmSubscription.create(
            "测试订阅",
            new UserId(1L),
            "测试订阅描述"
        );
    }
}
