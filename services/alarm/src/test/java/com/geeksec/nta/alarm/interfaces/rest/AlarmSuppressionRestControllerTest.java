package com.geeksec.nta.alarm.interfaces.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.nta.alarm.application.service.AlarmSuppressionCommandService;
import com.geeksec.nta.alarm.application.service.AlarmSuppressionQueryService;
import com.geeksec.nta.alarm.domain.aggregate.suppression.AlarmSuppression;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import com.geeksec.nta.alarm.interfaces.dto.request.CreateSuppressionRequest;
import com.geeksec.nta.alarm.interfaces.dto.request.UpdateSuppressionRequest;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmSuppressionResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 告警抑制REST控制器测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@WebMvcTest(AlarmSuppressionRestController.class)
@DisplayName("告警抑制REST控制器测试")
class AlarmSuppressionRestControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private AlarmSuppressionCommandService suppressionCommandService;

    @MockBean
    private AlarmSuppressionQueryService suppressionQueryService;

    private CreateSuppressionRequest createRequest;
    private UpdateSuppressionRequest updateRequest;
    private AlarmSuppression testSuppression;
    private AlarmSuppressionResponse suppressionResponse;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        createRequest = new CreateSuppressionRequest();
        createRequest.setVictim("*************");
        createRequest.setAttacker("********");
        createRequest.setLabel("SQL注入");
        createRequest.setSuppressionName("测试抑制规则");
        createRequest.setDescription("测试抑制规则描述");
        createRequest.setEnabled(true);
        createRequest.setExpiryTime(LocalDateTime.now().plusDays(30));
        createRequest.setNote("测试备注");

        updateRequest = new UpdateSuppressionRequest();
        updateRequest.setVictim("*************");
        updateRequest.setAttacker("********");
        updateRequest.setLabel("XSS攻击");
        updateRequest.setSuppressionName("更新的抑制规则");
        updateRequest.setDescription("更新的抑制规则描述");
        updateRequest.setEnabled(false);
        updateRequest.setExpiryTime(LocalDateTime.now().plusDays(60));
        updateRequest.setNote("更新备注");

        testSuppression = createTestSuppression();
        suppressionResponse = createSuppressionResponse();
    }

    @Test
    @DisplayName("创建抑制规则 - 成功")
    void createSuppression_Success() throws Exception {
        // Given
        when(suppressionCommandService.createSuppression(any()))
            .thenReturn(testSuppression);

        // When & Then
        mockMvc.perform(post("/api/v1/alarm-suppressions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists());
    }

    @Test
    @DisplayName("更新抑制规则 - 成功")
    void updateSuppression_Success() throws Exception {
        // Given
        String suppressionId = "suppression-123";
        when(suppressionCommandService.updateSuppression(any()))
            .thenReturn(testSuppression);

        // When & Then
        mockMvc.perform(put("/api/v1/alarm-suppressions/{id}", suppressionId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists());
    }

    @Test
    @DisplayName("删除抑制规则 - 成功")
    void deleteSuppression_Success() throws Exception {
        // Given
        String suppressionId = "suppression-123";
        when(suppressionCommandService.deleteSuppression(any()))
            .thenReturn(true);

        // When & Then
        mockMvc.perform(delete("/api/v1/alarm-suppressions/{id}", suppressionId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @DisplayName("查询抑制规则列表 - 成功")
    void getSuppressionList_Success() throws Exception {
        // Given
        List<AlarmSuppressionResponse> suppressions = Arrays.asList(suppressionResponse);
        when(suppressionQueryService.getSuppressionList(any()))
            .thenReturn(suppressions);

        // When & Then
        mockMvc.perform(get("/api/v1/alarm-suppressions")
                .param("page", "1")
                .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    @DisplayName("获取抑制规则详情 - 成功")
    void getSuppressionDetail_Success() throws Exception {
        // Given
        String suppressionId = "suppression-123";
        when(suppressionQueryService.getSuppressionDetail(any()))
            .thenReturn(suppressionResponse);

        // When & Then
        mockMvc.perform(get("/api/v1/alarm-suppressions/{id}", suppressionId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists());
    }

    @Test
    @DisplayName("创建抑制规则 - 参数验证失败")
    void createSuppression_ValidationFailed() throws Exception {
        // Given - 创建无效的请求（缺少必填字段）
        CreateSuppressionRequest invalidRequest = new CreateSuppressionRequest();

        // When & Then
        mockMvc.perform(post("/api/v1/alarm-suppressions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }

    // ==================== 辅助方法 ====================

    private AlarmSuppression createTestSuppression() {
        return AlarmSuppression.create(
            "*************",
            "********",
            "SQL注入",
            "测试抑制规则",
            "测试抑制规则描述",
            LocalDateTime.now().plusDays(30),
            "testuser"
        );
    }

    private AlarmSuppressionResponse createSuppressionResponse() {
        AlarmSuppressionResponse response = new AlarmSuppressionResponse();
        response.setId("suppression-123");
        response.setVictim("*************");
        response.setAttacker("********");
        response.setLabel("SQL注入");
        response.setSuppressionName("测试抑制规则");
        response.setDescription("测试抑制规则描述");
        response.setEnabled(true);
        response.setCreateTime(LocalDateTime.now());
        return response;
    }
}
