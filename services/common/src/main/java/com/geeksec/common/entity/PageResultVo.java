package com.geeksec.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 分页结果对象
 *
 * 统一的分页响应格式，支持：
 * - 基础分页信息
 * - ES限制总数（前端分页优化）
 * - 分页计算工具方法
 *
 * <AUTHOR>
 * @param <T> 数据类型
 */
@Schema(description = "分页结果")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResultVo<T> implements Serializable {

    @Schema(description = "数据集合")
    private List<T> records;

    @Schema(description = "总记录数")
    private long total = 0;

    @Schema(description = "当前页码", example = "1")
    private int currentPage = 1;

    @Schema(description = "每页大小", example = "20")
    private int pageSize = 20;

    @Schema(description = "总页数", example = "5")
    private int totalPages = 0;

    @Schema(description = "是否有下一页", example = "true")
    private boolean hasNext = false;

    @Schema(description = "是否有上一页", example = "false")
    private boolean hasPrevious = false;

    @JsonProperty("es_limit_total")
    @Schema(description = "ES限制总数（前端分页优化）")
    private long esLimitTotal;

    /**
     * 构造函数 - 自动计算分页信息
     */
    public PageResultVo(List<T> records, long total, int currentPage, int pageSize) {
        this.records = records;
        this.total = total;
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        this.totalPages = (int) Math.ceil((double) total / pageSize);
        this.hasNext = currentPage < totalPages;
        this.hasPrevious = currentPage > 1;
    }

    public long getEsLimitTotal() {
        if(esLimitTotal > 0)
            return esLimitTotal;
        if(total > 10000){
            //前端es分页列表仅 1W
            return 10000;
        }else{
            return total;
        }
    }

    public void setEsLimitTotal(long esLimitTotal) {
        this.esLimitTotal = esLimitTotal;
    }

    /**
     * 内存分页工具方法
     * 
     * @param list     分页前的集合
     * @param pageNum  页码
     * @param pageSize 页数
     * @param <T>
     * @return 分页后的集合
     */
    public static <T> List<T> pageList(List<T> list, int pageNum, int pageSize) {
        if(list == null || list.size() < 1){
            return list;
        }
        //计算总页数
        int page = list.size() % pageSize == 0 ? list.size() / pageSize : list.size() / pageSize + 1;
        //兼容性分页参数错误
        pageNum = pageNum <= 0 ? 1 : pageNum;
        pageNum = pageNum >= page ? page : pageNum;
        // 开始索引
        int begin = 0;
        // 结束索引
        int end = 0;
        if (pageNum != page) {
            begin = (pageNum - 1) * pageSize;
            end = begin + pageSize;
        } else {
            begin = (pageNum - 1) * pageSize;
            end = list.size();
        }
        return list.subList(begin, end);
    }

    /**
     * 计算总页数
     *
     * @param pageSize  每页大小
     * @param itemCount 总记录数
     * @return 总页数
     */
    public static int calculateTotalPages(int pageSize, long itemCount) {
        if (itemCount == 0) {
            return 1;
        }
        return (int) (itemCount % pageSize > 0 ?
                itemCount / pageSize + 1 :
                itemCount / pageSize);
    }

    /**
     * 分页参数处理工具方法
     * 将前端传入的分页参数转换为标准格式
     *
     * @param paramObject    查询条件Map
     * @param defaultPageSize 默认每页大小
     */
    public static void fillPageParam(Map<String, Object> paramObject, int defaultPageSize) {
        Object pageNumObj = paramObject.get("pageNum");
        Object pageRowObj = paramObject.get("pageRow");
        
        int pageNum = pageNumObj != null ? Integer.parseInt(pageNumObj.toString()) : 1;
        pageNum = pageNum == 0 ? 1 : pageNum;
        
        int pageRow = pageRowObj != null ? Integer.parseInt(pageRowObj.toString()) : defaultPageSize;
        pageRow = pageRow == 0 ? defaultPageSize : pageRow;
        
        paramObject.put("offSet", (pageNum - 1) * pageRow);
        paramObject.put("pageRow", pageRow);
        paramObject.put("pageNum", pageNum);
        // 删除此参数,统一使用pageRow作为分页大小参数
        paramObject.remove("pageSize");
    }

    /**
     * 创建分页结果的静态工厂方法
     *
     * @param records 数据列表
     * @param total 总记录数
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @param <T> 数据类型
     * @return 分页结果
     */
    public static <T> PageResultVo<T> of(List<T> records, long total, long currentPage, long pageSize) {
        return new PageResultVo<>(records, total, (int) currentPage, (int) pageSize);
    }

}
