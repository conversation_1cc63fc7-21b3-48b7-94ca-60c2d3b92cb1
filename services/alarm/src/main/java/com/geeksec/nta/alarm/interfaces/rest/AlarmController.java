package com.geeksec.nta.alarm.interfaces.rest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.common.entity.ResultVo;
import com.geeksec.nta.alarm.application.service.AlarmCommandService;
import com.geeksec.nta.alarm.application.service.AlarmQueryService;
import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.geeksec.nta.alarm.interfaces.converter.AlarmDtoConverter;
import com.geeksec.nta.alarm.interfaces.dto.request.AlarmUpdateRequest;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmListResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 告警REST控制器
 *
 * 处理告警相关的HTTP请求，遵循DDD架构的接口层设计
 *
 * 注意：告警创建由Flink作业负责，此API仅提供查询、更新、删除功能
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/alarms")
@RequiredArgsConstructor
@Tag(name = "告警管理", description = "告警查询、更新、删除API。注意：告警创建由Flink作业负责。")
public class AlarmController {

    private final AlarmCommandService alarmCommandService;
    private final AlarmQueryService alarmQueryService;
    private final AlarmDtoConverter alarmDtoConverter;

    /**
     * 更新告警
     */
    @PutMapping("/{alarmId}")
    @Operation(summary = "更新告警", description = "更新告警信息")
    public ResultVo<AlarmListResponse> updateAlarm(
            @PathVariable String alarmId,
            @RequestBody AlarmUpdateRequest request) {
        try {
            AlarmId id = AlarmId.of(alarmId);

            // 调用应用服务更新告警
            Alarm alarm = alarmCommandService.updateAlarm(id, request);

            // 转换为响应DTO
            AlarmListResponse response = alarmDtoConverter.toResponse(alarm);

            return ResultVo.success(response);
        } catch (Exception e) {
            log.error("更新告警失败: alarmId={}", alarmId, e);
            return ResultVo.error("更新告警失败: " + e.getMessage());
        }
    }

    /**
     * 删除告警
     */
    @DeleteMapping("/{alarmId}")
    @Operation(summary = "删除告警", description = "删除指定告警")
    public ResultVo<Boolean> deleteAlarm(@PathVariable String alarmId) {
        try {
            AlarmId id = AlarmId.of(alarmId);

            boolean result = alarmCommandService.deleteAlarm(id);

            return ResultVo.success(result);
        } catch (Exception e) {
            log.error("删除告警失败: alarmId={}", alarmId, e);
            return ResultVo.error("删除告警失败: " + e.getMessage());
        }
    }

    /**
     * 获取告警详情
     */
    @GetMapping("/{alarmId}")
    @Operation(summary = "获取告警详情", description = "根据ID获取告警详细信息")
    public ResultVo<AlarmListResponse> getAlarm(@PathVariable String alarmId) {
        try {
            AlarmId id = AlarmId.of(alarmId);

            Alarm alarm = alarmQueryService.findById(id);
            if (alarm == null) {
                return ResultVo.error("告警不存在");
            }

            AlarmListResponse response = alarmDtoConverter.toResponse(alarm);

            return ResultVo.success(response);
        } catch (Exception e) {
            log.error("获取告警详情失败: alarmId={}", alarmId, e);
            return ResultVo.error("获取告警详情失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询告警列表
     */
    @GetMapping
    @Operation(summary = "分页查询告警", description = "根据条件分页查询告警列表")
    public ResultVo<PageResultVo<AlarmListResponse>> getAlarms(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String alarmName,
            @RequestParam(required = false) String alarmType,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer threatLevel) {
        try {
            // 构建查询条件
            Map<String, Object> conditions = new HashMap<>();
            if (alarmName != null && !alarmName.trim().isEmpty()) {
                conditions.put("alarmName", alarmName);
            }
            if (alarmType != null && !alarmType.trim().isEmpty()) {
                conditions.put("alarmType", alarmType);
            }
            if (status != null) {
                conditions.put("status", status);
            }
            if (threatLevel != null) {
                conditions.put("threatLevel", threatLevel);
            }

            // 调用查询服务
            PageResultVo<Alarm> pageResult = alarmQueryService.findByConditions(conditions, page, size);

            // 转换为响应DTO
            List<AlarmListResponse> responses = pageResult.getRecords().stream()
                    .map(alarmDtoConverter::toResponse)
                    .toList();

            PageResultVo<AlarmListResponse> result = new PageResultVo<>();
            result.setRecords(responses);
            result.setTotal(pageResult.getTotal());
            result.setCurrent(pageResult.getCurrent());
            result.setSize(pageResult.getSize());

            return ResultVo.success(result);
        } catch (Exception e) {
            log.error("查询告警列表失败", e);
            return ResultVo.error("查询告警列表失败: " + e.getMessage());
        }
    }

    /**
     * 批量管理告警状态
     */
    @PostMapping("/batch-manage")
    @Operation(summary = "批量管理告警状态", description = "批量管理多个告警的状态（仅支持状态管理操作）")
    public ResultVo<Map<String, Object>> batchManageAlarms(
            @RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<String> alarmIds = (List<String>) request.get("alarmIds");
            String action = (String) request.get("action");
            Integer processor = (Integer) request.get("processor");

            if (alarmIds == null || alarmIds.isEmpty()) {
                return ResultVo.error("告警ID列表不能为空");
            }

            // 验证操作类型
            AlarmCommandService.BatchAction batchAction;
            try {
                batchAction = AlarmCommandService.BatchAction.valueOf(action);
            } catch (IllegalArgumentException e) {
                return ResultVo.error("不支持的操作类型: " + action);
            }

            // 转换为领域对象
            List<AlarmId> ids = alarmIds.stream()
                    .map(AlarmId::of)
                    .toList();

            // 调用应用服务批量管理状态
            AlarmCommandService.BatchProcessResult result = alarmCommandService.batchManageAlarms(ids, processor, batchAction);

            Map<String, Object> response = new HashMap<>();
            response.put("totalCount", result.totalCount());
            response.put("successCount", result.successCount());
            response.put("failureCount", result.failureCount());
            response.put("errorMessages", result.errorMessages());

            return ResultVo.success(response);
        } catch (Exception e) {
            log.error("批量管理告警状态失败", e);
            return ResultVo.error("批量管理告警状态失败: " + e.getMessage());
        }
    }

    /**
     * 告警统计
     */
    @GetMapping("/statistics")
    @Operation(summary = "告警统计", description = "获取告警统计信息")
    public ResultVo<Map<String, Object>> getStatistics(
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        try {
            Map<String, Object> statistics = alarmQueryService.getStatistics(startTime, endTime);
            return ResultVo.success(statistics);
        } catch (Exception e) {
            log.error("获取告警统计失败", e);
            return ResultVo.error("获取告警统计失败: " + e.getMessage());
        }
    }
}
