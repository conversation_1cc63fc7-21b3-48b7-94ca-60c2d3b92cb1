package com.geeksec.admin.interfaces.rest;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.admin.infrastructure.service.DiskManagementService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 磁盘管理控制器
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@Tag(name = "磁盘管理接口", description = "磁盘管理接口相关操作")
@RequestMapping("/api/raid")
public class DiskController {

    @Autowired
    private DiskManagementService diskManagementService;

    /**
     * 获取RAID卡磁盘信息
     *
     * @return RAID信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取RAID卡磁盘信息", description = "获取RAID卡磁盘信息操作")
    public ObjectNode getRaidInfo() {
        return diskManagementService.getRaidInfo();
    }

    /**
     * 获取可管理的磁盘列表
     *
     * @return 磁盘列表
     */
    @GetMapping("/disks")
    @Operation(summary = "获取可管理的磁盘列表", description = "获取可管理的磁盘列表操作")
    public ObjectNode getManageableDisks() {
        return diskManagementService.getManageableDisks();
    }

    /**
     * 磁盘重组操作
     *
     * @return 操作结果
     */
    @PostMapping("/rebuild")
    @Operation(summary = "磁盘重组操作", description = "磁盘重组操作")
    public ObjectNode rebuildDisk() {
        return diskManagementService.rebuildDisk();
    }

    /**
     * 磁盘更新操作
     *
     * @return 操作结果
     */
    @PostMapping("/update")
    @Operation(summary = "磁盘更新操作", description = "磁盘更新操作")
    public ObjectNode updateDisk() {
        return diskManagementService.updateDisk();
    }

    /**
     * 准备挂载磁盘
     *
     * @return 操作结果
     */
    @PostMapping("/mount/prepare")
    @Operation(summary = "准备挂载磁盘", description = "准备挂载磁盘操作")
    public ObjectNode prepareMount() {
        return diskManagementService.prepareMount();
    }

    /**
     * 挂载数据磁盘
     *
     * @return 操作结果
     */
    @PostMapping("/mount/data")
    @Operation(summary = "挂载数据磁盘", description = "挂载数据磁盘操作")
    public ObjectNode mountDataDisk() {
        return diskManagementService.mountDataDisk();
    }

    /**
     * 格式化磁盘
     *
     * @param request 格式化请求
     * @return 操作结果
     */
    @PostMapping("/format")
    @Operation(summary = "格式化磁盘", description = "格式化磁盘操作")
    public ApiResponse<ObjectNode> formatDisk(@RequestBody JsonNode request) {
        try {
            String devicePath = request.has("device_path") ? request.get("device_path").asText() : null;
            String fileSystem = request.has("file_system") ? request.get("file_system").asText() : null;
            
            if (devicePath == null || fileSystem == null) {
                return ApiResponse.error("设备路径和文件系统类型不能为空");
            }
            
            ObjectNode result = diskManagementService.formatDisk(devicePath, fileSystem);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("格式化磁盘失败", e);
            return ApiResponse.error("格式化磁盘失败: " + e.getMessage());
        }
    }

    /**
     * 挂载磁盘
     *
     * @param request 挂载请求
     * @return 操作结果
     */
    @PostMapping("/mount")
    @Operation(summary = "挂载磁盘", description = "挂载磁盘操作")
    public ApiResponse<ObjectNode> mountDisk(@RequestBody JsonNode request) {
        try {
            String devicePath = request.has("device_path") ? request.get("device_path").asText() : null;
            String mountPoint = request.has("mount_point") ? request.get("mount_point").asText() : null;
            
            if (devicePath == null || mountPoint == null) {
                return ApiResponse.error("设备路径和挂载点不能为空");
            }
            
            ObjectNode result = diskManagementService.mountDisk(devicePath, mountPoint);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("挂载磁盘失败", e);
            return ApiResponse.error("挂载磁盘失败: " + e.getMessage());
        }
    }

    /**
     * 卸载磁盘
     *
     * @param request 卸载请求
     * @return 操作结果
     */
    @PostMapping("/unmount")
    @Operation(summary = "卸载磁盘", description = "卸载磁盘操作")
    public ApiResponse<ObjectNode> unmountDisk(@RequestBody JsonNode request) {
        try {
            String mountPoint = request.has("mount_point") ? request.get("mount_point").asText() : null;
            
            if (mountPoint == null) {
                return ApiResponse.error("挂载点不能为空");
            }
            
            ObjectNode result = diskManagementService.unmountDisk(mountPoint);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("卸载磁盘失败", e);
            return ApiResponse.error("卸载磁盘失败: " + e.getMessage());
        }
    }
}
