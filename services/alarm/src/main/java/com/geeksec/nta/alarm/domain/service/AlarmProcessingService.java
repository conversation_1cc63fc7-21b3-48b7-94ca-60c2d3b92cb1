package com.geeksec.nta.alarm.domain.service;

import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.valueobject.AlarmStatusEnum;
import com.geeksec.nta.alarm.domain.valueobject.ThreatLevel;

import java.util.List;
import java.util.Objects;

/**
 * 告警处理领域服务
 *
 * 负责告警的处理逻辑，包括状态转换、威胁等级评估、处理流程控制等
 * 这是一个纯领域服务，不依赖任何框架
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class AlarmProcessingService {

    /**
     * 开始处理告警
     * 
     * @param alarm 告警
     * @param processor 处理者
     */
    public void startProcessing(Alarm alarm, Integer processor) {
        Objects.requireNonNull(alarm, "告警不能为空");
        Objects.requireNonNull(processor, "处理者不能为空");
        
        // 开始处理告警
        
        // 检查告警当前状态
        if (alarm.getAlarmStatus() != AlarmStatusEnum.NEW) {
            throw new IllegalStateException("只有新建的告警才能开始处理");
        }
        
        // 更新状态为处理中
        alarm.updateStatus(AlarmStatusEnum.PROCESSING, processor);
        
        // 告警已开始处理
    }

    /**
     * 完成告警处理
     * 
     * @param alarm 告警
     * @param processor 处理者
     * @param processResult 处理结果
     */
    public void completeProcessing(Alarm alarm, Integer processor, ProcessResult processResult) {
        Objects.requireNonNull(alarm, "告警不能为空");
        Objects.requireNonNull(processor, "处理者不能为空");
        Objects.requireNonNull(processResult, "处理结果不能为空");
        
        // 完成告警处理
        
        // 检查告警当前状态
        if (alarm.getAlarmStatus() != AlarmStatusEnum.PROCESSING) {
            throw new IllegalStateException("只有处理中的告警才能完成处理");
        }
        
        // 根据处理结果更新状态
        AlarmStatusEnum targetStatus = processResult.shouldClose() ? AlarmStatusEnum.CLOSED : AlarmStatusEnum.PROCESSED;
        alarm.updateStatus(targetStatus, processor);
        
        // 如果有处理备注，可以添加到告警中
        if (processResult.hasRemark()) {
            // TODO: 添加处理备注到告警中
        }

        // 告警处理完成
    }

    /**
     * 关闭告警
     * 
     * @param alarm 告警
     * @param operator 操作者
     * @param reason 关闭原因
     */
    public void closeAlarm(Alarm alarm, Integer operator, String reason) {
        Objects.requireNonNull(alarm, "告警不能为空");
        Objects.requireNonNull(operator, "操作者不能为空");
        
        // 关闭告警
        
        // 检查告警当前状态
        AlarmStatusEnum currentStatus = alarm.getAlarmStatus();
        if (currentStatus == AlarmStatusEnum.CLOSED) {
            // 告警已经是关闭状态
            return;
        }
        
        // 更新状态为已关闭
        alarm.updateStatus(AlarmStatusEnum.CLOSED, operator);
        
        // 告警已关闭
    }

    /**
     * 批量处理告警
     * 
     * @param alarms 告警列表
     * @param processor 处理者
     * @param batchAction 批量操作
     */
    public BatchProcessResult batchProcess(List<Alarm> alarms, Integer processor, BatchAction batchAction) {
        Objects.requireNonNull(alarms, "告警列表不能为空");
        Objects.requireNonNull(processor, "处理者不能为空");
        Objects.requireNonNull(batchAction, "批量操作不能为空");
        
        // 批量处理告警
        
        int successCount = 0;
        int failureCount = 0;
        StringBuilder errorMessages = new StringBuilder();
        
        for (Alarm alarm : alarms) {
            try {
                switch (batchAction) {
                    case START_PROCESSING -> startProcessing(alarm, processor);
                    case CLOSE -> closeAlarm(alarm, processor, "批量关闭");
                    default -> throw new IllegalArgumentException("不支持的批量操作: " + batchAction);
                }
                successCount++;
            } catch (Exception e) {
                failureCount++;
                errorMessages.append(String.format("告警 %s 处理失败: %s; ", 
                    alarm.getAlarmId().getValue(), e.getMessage()));
                // 记录处理失败
            }
        }
        
        // 批量处理完成
        
        return new BatchProcessResult(
            successCount,
            failureCount,
            errorMessages.toString()
        );
    }

    /**
     * 评估告警威胁等级
     * 
     * @param alarm 告警
     * @return 建议的威胁等级
     */
    public ThreatLevel assessThreatLevel(Alarm alarm) {
        Objects.requireNonNull(alarm, "告警不能为空");
        
        // 基于告警类型、攻击者数量、受害者数量等因素评估威胁等级
        ThreatLevel currentLevel = alarm.getThreatLevel();
        
        // 如果有多个攻击者，提升威胁等级
        if (alarm.getAttackerIps().size() > 3) {
            if (currentLevel.getLevel() < ThreatLevel.HIGH.getLevel()) {
                return ThreatLevel.HIGH;
            }
        }
        
        // 如果有多个受害者，提升威胁等级
        if (alarm.getVictimIps().size() > 5) {
            if (currentLevel.getLevel() < ThreatLevel.CRITICAL.getLevel()) {
                return ThreatLevel.CRITICAL;
            }
        }
        
        return currentLevel;
    }

    /**
     * 处理结果
     */
    public static class ProcessResult {
        private final String result;
        private final String remark;
        private final boolean shouldClose;

        public ProcessResult(String result, String remark, boolean shouldClose) {
            this.result = Objects.requireNonNull(result, "处理结果不能为空");
            this.remark = remark;
            this.shouldClose = shouldClose;
        }

        public String getResult() { return result; }
        public String getRemark() { return remark; }
        public boolean shouldClose() { return shouldClose; }
        public boolean hasRemark() { return remark != null && !remark.trim().isEmpty(); }
    }

    /**
     * 批量操作类型
     */
    public enum BatchAction {
        START_PROCESSING,
        CLOSE
    }

    /**
     * 批量处理结果
     */
    public record BatchProcessResult(
        int successCount,
        int failureCount,
        String errorMessages
    ) {
        public boolean isAllSuccess() {
            return failureCount == 0;
        }
        
        public int getTotalCount() {
            return successCount + failureCount;
        }
    }
}
