package com.geeksec.nta.alarm.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 标签目标类型枚举
 * 对应数据库中的label_target_type_enum
 * 
 * <AUTHOR>
 * @Description：定义标签可以应用的目标类型
 */
@Getter
@AllArgsConstructor
public enum LabelTargetTypeEnum {

    /**
     * IP地址标签
     */
    IP("IP", "IP地址"),

    /**
     * 应用标签
     */
    APPLICATION("APPLICATION", "应用"),

    /**
     * 域名标签
     */
    DOMAIN("DOMAIN", "域名"),

    /**
     * 证书标签
     */
    CERTIFICATE("CERTIFICATE", "证书"),

    /**
     * 会话标签
     */
    SESSION("SESSION", "会话"),

    /**
     * 指纹标签
     */
    FINGERPRINT("FINGERPRINT", "指纹"),

    /**
     * MAC地址标签
     */
    MAC("MAC", "MAC地址"),

    /**
     * 端口标签
     */
    PORT("PORT", "端口");

    /**
     * 枚举值（对应数据库枚举值）
     */
    private final String value;

    /**
     * 中文描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     * 
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static LabelTargetTypeEnum fromValue(String value) {
        for (LabelTargetTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查值是否有效
     * 
     * @param value 要检查的值
     * @return 是否为有效的枚举值
     */
    public static boolean isValid(String value) {
        return fromValue(value) != null;
    }
}
