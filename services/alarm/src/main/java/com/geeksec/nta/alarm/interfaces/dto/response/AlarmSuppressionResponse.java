package com.geeksec.nta.alarm.interfaces.dto.response;

import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 告警抑制响应对象
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@Jacksonized
public class AlarmSuppressionResponse {

    /**
     * 抑制规则ID
     */
    private Long suppressionId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 抑制类型
     */
    private String suppressionType;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 匹配条件
     */
    private List<SuppressionCondition> conditions;

    /**
     * 抑制动作
     */
    private String action;

    /**
     * 抑制时长（分钟）
     */
    private Integer durationMinutes;

    /**
     * 生效时间段
     */
    private List<TimeRange> effectiveTimeRanges;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最后触发时间
     */
    private LocalDateTime lastTriggeredTime;

    /**
     * 触发次数
     */
    private Integer triggerCount;

    /**
     * 抑制告警数量
     */
    private Integer suppressedAlarmCount;

    /**
     * 抑制条件
     */
    @Data
    @Builder
    @Jacksonized
    public static class SuppressionCondition {
        /**
         * 字段名
         */
        private String field;

        /**
         * 操作符
         */
        private String operator;

        /**
         * 匹配值
         */
        private String value;
    }

    /**
     * 时间范围
     */
    @Data
    @Builder
    @Jacksonized
    public static class TimeRange {
        /**
         * 开始时间
         */
        private String startTime;

        /**
         * 结束时间
         */
        private String endTime;
    }

    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return ruleName != null ? ruleName : "抑制规则-" + suppressionId;
    }

    /**
     * 获取状态颜色
     */
    public String getStatusColor() {
        if (Boolean.TRUE.equals(enabled)) {
            return "success";
        } else {
            return "default";
        }
    }

    /**
     * 是否有时间限制
     */
    public boolean hasTimeLimit() {
        return durationMinutes != null && durationMinutes > 0;
    }

    /**
     * 是否有时长限制
     */
    public boolean hasDurationLimit() {
        return effectiveTimeRanges != null && !effectiveTimeRanges.isEmpty();
    }

    /**
     * 格式化时长
     */
    public String getFormattedDuration() {
        if (durationMinutes == null) {
            return "永久";
        }
        if (durationMinutes < 60) {
            return durationMinutes + "分钟";
        } else if (durationMinutes < 1440) {
            return (durationMinutes / 60) + "小时";
        } else {
            return (durationMinutes / 1440) + "天";
        }
    }

    /**
     * 获取条件数量
     */
    public int getConditionCount() {
        return conditions != null ? conditions.size() : 0;
    }
}