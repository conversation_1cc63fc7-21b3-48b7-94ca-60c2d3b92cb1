package com.geeksec.nta.alarm.application.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.geeksec.nta.alarm.application.command.DeleteAlarmCommand;
import com.geeksec.nta.alarm.application.command.UpdateAlarmStatusCommand;
import com.geeksec.nta.alarm.application.service.AlarmCommandService;
import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.repository.AlarmRepository;
import com.geeksec.nta.alarm.domain.service.AlarmProcessingService;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.geeksec.nta.alarm.domain.valueobject.ThreatLevel;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 告警命令应用服务实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlarmCommandServiceImpl implements AlarmCommandService {

    private final AlarmRepository alarmRepository;
    private final AlarmProcessingService alarmProcessingService;

    @Override
    @Transactional
    public Alarm createAlarm(String alarmName, String alarmType, Integer threatLevel, 
                           Date alarmTime, Integer createdBy) {
        log.info("创建告警: name={}, type={}, threatLevel={}", alarmName, alarmType, threatLevel);
        
        try {
            Alarm alarm = Alarm.create(alarmName, alarmType, threatLevel, alarmTime, createdBy);
            Alarm savedAlarm = alarmRepository.save(alarm);
            
            log.info("告警创建成功: id={}", savedAlarm.getId());
            return savedAlarm;
        } catch (Exception e) {
            log.error("创建告警失败: name={}", alarmName, e);
            throw new RuntimeException("创建告警失败", e);
        }
    }

    @Override
    @Transactional
    public boolean startProcessing(AlarmId alarmId, Integer processor) {
        log.info("开始处理告警: alarmId={}, processor={}", alarmId.getValue(), processor);
        
        try {
            Optional<Alarm> alarmOpt = alarmRepository.findById(alarmId);
            if (alarmOpt.isEmpty()) {
                log.warn("告警不存在: {}", alarmId.getValue());
                return false;
            }
            
            Alarm alarm = alarmOpt.get();
            alarmProcessingService.startProcessing(alarm, processor);
            alarmRepository.save(alarm);
            
            log.info("开始处理告警成功: {}", alarmId.getValue());
            return true;
        } catch (Exception e) {
            log.error("开始处理告警失败: {}", alarmId.getValue(), e);
            throw new RuntimeException("开始处理告警失败", e);
        }
    }

    @Override
    @Transactional
    public boolean completeProcessing(AlarmId alarmId, Integer processor, String processResult) {
        log.info("完成告警处理: alarmId={}, processor={}", alarmId.getValue(), processor);
        
        try {
            Optional<Alarm> alarmOpt = alarmRepository.findById(alarmId);
            if (alarmOpt.isEmpty()) {
                log.warn("告警不存在: {}", alarmId.getValue());
                return false;
            }
            
            Alarm alarm = alarmOpt.get();
            alarmProcessingService.completeProcessing(alarm, processor, processResult);
            alarmRepository.save(alarm);
            
            log.info("完成告警处理成功: {}", alarmId.getValue());
            return true;
        } catch (Exception e) {
            log.error("完成告警处理失败: {}", alarmId.getValue(), e);
            throw new RuntimeException("完成告警处理失败", e);
        }
    }

    @Override
    @Transactional
    public boolean upgradeThreatLevel(AlarmId alarmId, ThreatLevel newThreatLevel, Integer updatedBy) {
        log.info("提升威胁等级: alarmId={}, newLevel={}", alarmId.getValue(), newThreatLevel.getLevel());
        
        try {
            Optional<Alarm> alarmOpt = alarmRepository.findById(alarmId);
            if (alarmOpt.isEmpty()) {
                log.warn("告警不存在: {}", alarmId.getValue());
                return false;
            }
            
            Alarm alarm = alarmOpt.get();
            alarm.upgradeThreatLevel(newThreatLevel, updatedBy);
            alarmRepository.save(alarm);
            
            log.info("提升威胁等级成功: {}", alarmId.getValue());
            return true;
        } catch (Exception e) {
            log.error("提升威胁等级失败: {}", alarmId.getValue(), e);
            throw new RuntimeException("提升威胁等级失败", e);
        }
    }

    @Override
    @Transactional
    public boolean updateAlarmStatus(UpdateAlarmStatusCommand command) {
        log.info("更新告警状态: alarmIds={}, status={}", command.getAlarmIds(), command.getNewStatus());
        
        try {
            // 处理批量更新
            int successCount = 0;
            for (AlarmId alarmId : command.getAlarmIds()) {
                Optional<Alarm> alarmOpt = alarmRepository.findById(alarmId);
                if (alarmOpt.isEmpty()) {
                    log.warn("告警不存在: {}", alarmId.getValue());
                    continue;
                }
                
                Alarm alarm = alarmOpt.get();
                alarm.updateStatus(command.getNewStatus(), command.getOperator());
                alarmRepository.save(alarm);
                successCount++;
            }
            
            log.info("更新告警状态成功: 成功数量={}", successCount);
            return successCount > 0;
        } catch (Exception e) {
            log.error("更新告警状态失败: alarmIds={}", command.getAlarmIds(), e);
            throw new RuntimeException("更新告警状态失败", e);
        }
    }

    @Override
    @Transactional
    public int batchUpdateAlarmStatus(List<UpdateAlarmStatusCommand> commands) {
        log.info("批量更新告警状态: 数量={}", commands.size());
        
        int successCount = 0;
        for (UpdateAlarmStatusCommand command : commands) {
            try {
                if (updateAlarmStatus(command)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量更新告警状态失败: alarmIds={}", command.getAlarmIds(), e);
            }
        }
        
        log.info("批量更新告警状态完成: 成功={}, 总数={}", successCount, commands.size());
        return successCount;
    }

    @Override
    @Transactional
    public BatchProcessResult batchProcessAlarms(List<AlarmId> alarmIds, Integer processor, BatchAction action) {
        log.info("批量处理告警: 数量={}, 操作={}", alarmIds.size(), action.getDescription());
        
        int successCount = 0;
        int failureCount = 0;
        List<String> errorMessages = new ArrayList<>();
        
        for (AlarmId alarmId : alarmIds) {
            try {
                boolean success = switch (action) {
                    case START_PROCESSING -> startProcessing(alarmId, processor);
                    case COMPLETE_PROCESSING -> completeProcessing(alarmId, processor, "批量处理完成");
                    case MARK_FALSE_POSITIVE -> markFalsePositive(alarmId, processor);
                    case IGNORE -> ignoreAlarm(alarmId, processor);
                };
                
                if (success) {
                    successCount++;
                } else {
                    failureCount++;
                    errorMessages.add(String.format("告警 %s 处理失败", alarmId.getValue()));
                }
            } catch (Exception e) {
                failureCount++;
                errorMessages.add(String.format("告警 %s 处理异常: %s", alarmId.getValue(), e.getMessage()));
                log.error("批量处理告警失败: alarmId={}", alarmId.getValue(), e);
            }
        }
        
        log.info("批量处理告警完成: 成功={}, 失败={}", successCount, failureCount);
        return new BatchProcessResult(alarmIds.size(), successCount, failureCount, errorMessages);
    }

    @Override
    @Transactional
    public boolean deleteAlarm(DeleteAlarmCommand command) {
        log.info("删除告警: alarmId={}", command.getAlarmId().getValue());
        
        try {
            boolean deleted = alarmRepository.deleteById(command.getAlarmId());
            
            if (deleted) {
                log.info("删除告警成功: {}", command.getAlarmId().getValue());
            } else {
                log.warn("告警不存在或删除失败: {}", command.getAlarmId().getValue());
            }
            
            return deleted;
        } catch (Exception e) {
            log.error("删除告警失败: {}", command.getAlarmId().getValue(), e);
            throw new RuntimeException("删除告警失败", e);
        }
    }

    @Override
    @Transactional
    public int batchDeleteAlarms(List<DeleteAlarmCommand> commands) {
        log.info("批量删除告警: 数量={}", commands.size());
        
        int successCount = 0;
        for (DeleteAlarmCommand command : commands) {
            try {
                if (deleteAlarm(command)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量删除告警失败: alarmId={}", command.getAlarmId().getValue(), e);
            }
        }
        
        log.info("批量删除告警完成: 成功={}, 总数={}", successCount, commands.size());
        return successCount;
    }

    @Override
    @Transactional
    public long deleteAllAlarms() {
        log.warn("删除所有告警 - 危险操作");
        
        try {
            long deletedCount = alarmRepository.deleteAll();
            log.warn("删除所有告警完成: 删除数量={}", deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("删除所有告警失败", e);
            throw new RuntimeException("删除所有告警失败", e);
        }
    }

    @Override
    @Transactional
    public long archiveExpiredAlarms(int daysToKeep) {
        log.info("归档过期告警: 保留天数={}", daysToKeep);
        
        try {
            // TODO: 实现归档逻辑
            log.info("归档过期告警完成");
            return 0;
        } catch (Exception e) {
            log.error("归档过期告警失败", e);
            throw new RuntimeException("归档过期告警失败", e);
        }
    }

    // ==================== 私有方法 ====================

    private boolean markFalsePositive(AlarmId alarmId, Integer processor) {
        // TODO: 实现标记误报的逻辑
        return true;
    }

    private boolean ignoreAlarm(AlarmId alarmId, Integer processor) {
        // TODO: 实现忽略告警的逻辑
        return true;
    }
}
