package com.geeksec.nta.alarm.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 标签来源枚举
 * 对应数据库中的label_source_enum
 * 
 * <AUTHOR>
 * @Description：定义标签的来源类型
 */
@Getter
@AllArgsConstructor
public enum LabelSourceEnum {

    /**
     * 系统内置标签
     */
    SYSTEM("SYSTEM", "系统内置"),

    /**
     * 规则标签（来自检测规则）
     */
    RULE("RULE", "规则标签"),

    /**
     * 用户自定义标签
     */
    USER("USER", "用户自定义");

    /**
     * 枚举值（对应数据库枚举值）
     */
    private final String value;

    /**
     * 中文描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     * 
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static LabelSourceEnum fromValue(String value) {
        for (LabelSourceEnum source : values()) {
            if (source.getValue().equals(value)) {
                return source;
            }
        }
        return null;
    }

    /**
     * 检查值是否有效
     * 
     * @param value 要检查的值
     * @return 是否为有效的枚举值
     */
    public static boolean isValid(String value) {
        return fromValue(value) != null;
    }
}
