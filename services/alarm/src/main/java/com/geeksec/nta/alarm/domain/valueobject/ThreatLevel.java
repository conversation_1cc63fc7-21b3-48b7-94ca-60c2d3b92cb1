package com.geeksec.nta.alarm.domain.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 威胁等级值对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
@ToString
@EqualsAndHashCode
public final class ThreatLevel {

    /**
     * 低威胁等级
     */
    public static final ThreatLevel LOW = new ThreatLevel(1, "低");

    /**
     * 中威胁等级
     */
    public static final ThreatLevel MEDIUM = new ThreatLevel(2, "中");

    /**
     * 高威胁等级
     */
    public static final ThreatLevel HIGH = new ThreatLevel(3, "高");

    /**
     * 严重威胁等级
     */
    public static final ThreatLevel CRITICAL = new ThreatLevel(4, "严重");

    private final Integer level;
    private final String description;

    private ThreatLevel(Integer level, String description) {
        this.level = Objects.requireNonNull(level, "威胁等级不能为空");
        this.description = Objects.requireNonNull(description, "威胁等级描述不能为空");
        
        if (level < 1 || level > 4) {
            throw new IllegalArgumentException("威胁等级必须在1-4之间");
        }
    }

    /**
     * 根据等级值创建威胁等级
     *
     * @param level 等级值
     * @return 威胁等级
     */
    public static ThreatLevel fromLevel(Integer level) {
        if (level == null) {
            return LOW;
        }

        return switch (level) {
            case 1 -> LOW;
            case 2 -> MEDIUM;
            case 3 -> HIGH;
            case 4 -> CRITICAL;
            default -> throw new IllegalArgumentException("无效的威胁等级: " + level);
        };
    }

    /**
     * 根据等级值创建威胁等级（别名方法）
     *
     * @param level 等级值
     * @return 威胁等级
     */
    public static ThreatLevel of(Integer level) {
        return fromLevel(level);
    }

    /**
     * 是否为高威胁等级
     *
     * @return 是否为高威胁等级
     */
    public boolean isHighThreat() {
        return level >= 3;
    }

    /**
     * 是否为严重威胁等级
     *
     * @return 是否为严重威胁等级
     */
    public boolean isCritical() {
        return level == 4;
    }

    /**
     * 比较威胁等级
     *
     * @param other 其他威胁等级
     * @return 比较结果
     */
    public int compareTo(ThreatLevel other) {
        return Integer.compare(this.level, other.level);
    }

    /**
     * 是否高于指定威胁等级
     *
     * @param other 其他威胁等级
     * @return 是否高于
     */
    public boolean isHigherThan(ThreatLevel other) {
        return this.level > other.level;
    }
}
