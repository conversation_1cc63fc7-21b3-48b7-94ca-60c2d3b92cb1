package com.geeksec.nta.alarm.application.integration;

import com.geeksec.nta.alarm.application.query.AlarmListQuery;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警报告服务接口
 * 负责告警报告的生成和导出
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmReportService {
    
    /**
     * 生成告警PDF报告
     * 
     * @param query 查询条件
     * @return 报告文件路径
     */
    String generateAlarmPdfReport(AlarmListQuery query);
    
    /**
     * 生成告警Excel报告
     * 
     * @param query 查询条件
     * @return 报告文件路径
     */
    String generateAlarmExcelReport(AlarmListQuery query);
    
    /**
     * 生成告警统计报告
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param reportType 报告类型
     * @return 报告文件路径
     */
    String generateStatisticsReport(LocalDateTime startTime, LocalDateTime endTime, String reportType);
    
    /**
     * 获取报告生成状态
     * 
     * @param taskId 任务ID
     * @return 生成状态
     */
    ReportGenerationStatus getReportStatus(String taskId);
    
    /**
     * 取消报告生成任务
     * 
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean cancelReportGeneration(String taskId);

    // ==================== 内部类定义 ====================

    /**
     * 报告生成状态
     */
    enum ReportStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        COMPLETED("已完成"),
        FAILED("失败"),
        CANCELLED("已取消");

        private final String description;

        ReportStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 报告生成状态信息
     */
    record ReportGenerationStatus(
        String taskId,
        ReportStatus status,
        String filePath,
        long fileSize,
        LocalDateTime createdAt,
        LocalDateTime completedAt,
        String errorMessage,
        int progress
    ) {
        public boolean isCompleted() {
            return status == ReportStatus.COMPLETED;
        }
        
        public boolean isFailed() {
            return status == ReportStatus.FAILED;
        }
    }

    /**
     * 报告配置
     */
    record ReportConfig(
        String template,
        boolean includeCharts,
        boolean includeDetails,
        String format,
        List<String> sections
    ) {}
}
