package com.geeksec.nta.alarm.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 告警受害者值对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlarmVictim {
    
    /**
     * 受害者IP地址
     */
    private String ip;
    
    /**
     * 受害者端口
     */
    private Integer port;
    
    /**
     * 受害者主机名
     */
    private String hostname;
    
    /**
     * 受害者MAC地址
     */
    private String macAddress;
    
    /**
     * 受害者地理位置
     */
    private String location;
    
    /**
     * 受害者组织信息
     */
    private String organization;
    
    /**
     * 创建受害者对象
     * 
     * @param ip IP地址
     * @return 受害者对象
     */
    public static AlarmVictim of(String ip) {
        Objects.requireNonNull(ip, "受害者IP不能为空");
        AlarmVictim victim = new AlarmVictim();
        victim.ip = ip;
        return victim;
    }
    
    /**
     * 创建受害者对象
     * 
     * @param ip IP地址
     * @param port 端口
     * @return 受害者对象
     */
    public static AlarmVictim of(String ip, Integer port) {
        Objects.requireNonNull(ip, "受害者IP不能为空");
        AlarmVictim victim = new AlarmVictim();
        victim.ip = ip;
        victim.port = port;
        return victim;
    }
    
    /**
     * 获取受害者的完整地址
     * 
     * @return 完整地址
     */
    public String getFullAddress() {
        if (port != null && port > 0) {
            return ip + ":" + port;
        }
        return ip;
    }
    
    /**
     * 是否为内网地址
     * 
     * @return 是否为内网地址
     */
    public boolean isPrivateAddress() {
        if (ip == null) {
            return false;
        }
        
        return ip.startsWith("192.168.") || 
               ip.startsWith("10.") || 
               ip.startsWith("172.16.") ||
               ip.startsWith("172.17.") ||
               ip.startsWith("172.18.") ||
               ip.startsWith("172.19.") ||
               ip.startsWith("172.20.") ||
               ip.startsWith("172.21.") ||
               ip.startsWith("172.22.") ||
               ip.startsWith("172.23.") ||
               ip.startsWith("172.24.") ||
               ip.startsWith("172.25.") ||
               ip.startsWith("172.26.") ||
               ip.startsWith("172.27.") ||
               ip.startsWith("172.28.") ||
               ip.startsWith("172.29.") ||
               ip.startsWith("172.30.") ||
               ip.startsWith("172.31.");
    }
}
