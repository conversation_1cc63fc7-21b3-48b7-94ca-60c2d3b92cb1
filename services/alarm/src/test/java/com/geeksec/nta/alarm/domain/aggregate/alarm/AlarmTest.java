package com.geeksec.nta.alarm.domain.aggregate.alarm;

import com.geeksec.nta.alarm.domain.valueobject.AlarmStatusEnum;
import com.geeksec.nta.alarm.domain.event.AlarmCreatedEvent;
import com.geeksec.nta.alarm.domain.event.AlarmStatusChangedEvent;
import com.geeksec.nta.alarm.domain.event.AlarmThreatLevelChangedEvent;
import com.geeksec.nta.alarm.domain.valueobject.ThreatLevel;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Alarm聚合根单元测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@DisplayName("Alarm聚合根测试")
class AlarmTest {

    @Test
    @DisplayName("创建告警 - 成功场景")
    void create_whenValidParameters_thenSuccess() {
        // Given
        String alarmName = "SQL注入攻击";
        String alarmType = "WEB_ATTACK";
        Integer threatLevel = 3;
        Date alarmTime = new Date();
        Integer createdBy = 1001;

        // When
        Alarm alarm = Alarm.create(alarmName, alarmType, threatLevel, alarmTime, createdBy);

        // Then
        assertNotNull(alarm);
        assertEquals(alarmName, alarm.getAlarmName());
        assertEquals(alarmType, alarm.getAlarmType());
        assertEquals(threatLevel, alarm.getAttackLevel());
        assertEquals(alarmTime, alarm.getAlarmTime());
        assertEquals(createdBy, alarm.getCreatedBy());
        assertEquals(AlarmStatusEnum.NEW, alarm.getAlarmStatus());
        
        // 验证领域事件
        List<Object> domainEvents = alarm.getDomainEvents();
        assertEquals(1, domainEvents.size());
        assertTrue(domainEvents.get(0) instanceof AlarmCreatedEvent);
        
        AlarmCreatedEvent event = (AlarmCreatedEvent) domainEvents.get(0);
        assertEquals(alarm.getId(), event.getAlarmId());
        assertEquals(alarmType, event.getAlarmType());
    }

    @Test
    @DisplayName("创建告警 - 告警名称为空")
    void create_whenAlarmNameIsNull_thenThrowException() {
        // Given
        String alarmName = null;
        String alarmType = "WEB_ATTACK";
        Integer threatLevel = 3;
        Date alarmTime = new Date();
        Integer createdBy = 1001;

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> Alarm.create(alarmName, alarmType, threatLevel, alarmTime, createdBy)
        );
        assertEquals("告警名称不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("创建告警 - 威胁等级无效")
    void create_whenThreatLevelInvalid_thenThrowException() {
        // Given
        String alarmName = "SQL注入攻击";
        String alarmType = "WEB_ATTACK";
        Integer threatLevel = 5; // 无效等级
        Date alarmTime = new Date();
        Integer createdBy = 1001;

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> Alarm.create(alarmName, alarmType, threatLevel, alarmTime, createdBy)
        );
        assertEquals("威胁等级必须在1-4之间", exception.getMessage());
    }

    @Test
    @DisplayName("更新告警状态 - 成功场景")
    void updateStatus_whenValidTransition_thenSuccess() {
        // Given
        Alarm alarm = createTestAlarm();
        AlarmStatusEnum newStatus = AlarmStatusEnum.PROCESSING;
        Integer updatedBy = 1002;

        // When
        alarm.updateStatus(newStatus, updatedBy);

        // Then
        assertEquals(newStatus, alarm.getAlarmStatus());
        assertEquals(updatedBy, alarm.getUpdatedBy());
        assertNotNull(alarm.getUpdatedAt());
        
        // 验证领域事件
        List<Object> domainEvents = alarm.getDomainEvents();
        assertEquals(2, domainEvents.size()); // 创建事件 + 状态变更事件
        assertTrue(domainEvents.get(1) instanceof AlarmStatusChangedEvent);
        
        AlarmStatusChangedEvent event = (AlarmStatusChangedEvent) domainEvents.get(1);
        assertEquals(alarm.getId(), event.getAlarmId());
        assertEquals(AlarmStatusEnum.NEW, event.getOldStatus());
        assertEquals(newStatus, event.getNewStatus());
    }

    @Test
    @DisplayName("更新告警状态 - 无效状态转换")
    void updateStatus_whenInvalidTransition_thenThrowException() {
        // Given
        Alarm alarm = createTestAlarm();
        alarm.updateStatus(AlarmStatusEnum.PROCESSED, 1002); // 先设置为已处理
        
        // When & Then
        IllegalStateException exception = assertThrows(
            IllegalStateException.class,
            () -> alarm.updateStatus(AlarmStatusEnum.PROCESSING, 1002)
        );
        assertEquals("无效的状态转换: COMPLETED -> PROCESSING", exception.getMessage());
    }

    @Test
    @DisplayName("提升威胁等级 - 成功场景")
    void upgradeThreatLevel_whenValidUpgrade_thenSuccess() {
        // Given
        Alarm alarm = createTestAlarm();
        ThreatLevel newLevel = ThreatLevel.of(4); // 从3提升到4
        Integer updatedBy = 1002;

        // When
        alarm.upgradeThreatLevel(newLevel, updatedBy);

        // Then
        assertEquals(4, alarm.getAttackLevel());
        assertEquals(updatedBy, alarm.getUpdatedBy());
        assertNotNull(alarm.getUpdatedAt());
        
        // 验证领域事件
        List<Object> domainEvents = alarm.getDomainEvents();
        assertEquals(2, domainEvents.size());
        assertTrue(domainEvents.get(1) instanceof AlarmThreatLevelChangedEvent);
        
        AlarmThreatLevelChangedEvent event = (AlarmThreatLevelChangedEvent) domainEvents.get(1);
        assertEquals(alarm.getId(), event.getAlarmId());
        assertEquals(ThreatLevel.of(3), event.getOldThreatLevel());
        assertEquals(newLevel, event.getNewThreatLevel());
    }

    @Test
    @DisplayName("提升威胁等级 - 等级未提升")
    void upgradeThreatLevel_whenNotUpgrade_thenThrowException() {
        // Given
        Alarm alarm = createTestAlarm();
        ThreatLevel sameLevel = ThreatLevel.of(3); // 相同等级
        Integer updatedBy = 1002;

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> alarm.upgradeThreatLevel(sameLevel, updatedBy)
        );
        assertEquals("新威胁等级必须高于当前等级", exception.getMessage());
    }

    @Test
    @DisplayName("添加攻击者IP - 成功场景")
    void addAttackerIp_whenValidIp_thenSuccess() {
        // Given
        Alarm alarm = createTestAlarm();
        String attackerIp = "*************";

        // When
        alarm.addAttackerIp(attackerIp);

        // Then
        assertTrue(alarm.getAttackerIps().contains(attackerIp));
    }

    @Test
    @DisplayName("添加攻击者IP - IP为空")
    void addAttackerIp_whenIpIsNull_thenThrowException() {
        // Given
        Alarm alarm = createTestAlarm();

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> alarm.addAttackerIp(null)
        );
        assertEquals("攻击者IP不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("添加攻击者IP - 重复IP")
    void addAttackerIp_whenDuplicateIp_thenIgnore() {
        // Given
        Alarm alarm = createTestAlarm();
        String attackerIp = "*************";
        alarm.addAttackerIp(attackerIp);
        int originalSize = alarm.getAttackerIps().size();

        // When
        alarm.addAttackerIp(attackerIp); // 重复添加

        // Then
        assertEquals(originalSize, alarm.getAttackerIps().size());
    }

    @Test
    @DisplayName("验证告警数据 - 成功场景")
    void validate_whenValidData_thenNoException() {
        // Given
        Alarm alarm = createTestAlarm();

        // When & Then
        assertDoesNotThrow(alarm::validate);
    }

    @Test
    @DisplayName("验证告警数据 - 告警名称为空")
    void validate_whenAlarmNameIsEmpty_thenThrowException() {
        // Given
        Alarm alarm = createTestAlarm();
        alarm.setAlarmName(""); // 设置为空字符串

        // When & Then
        IllegalStateException exception = assertThrows(
            IllegalStateException.class,
            alarm::validate
        );
        assertEquals("告警名称不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("检查是否为高威胁等级")
    void isHighThreat_whenThreatLevelIsHigh_thenReturnTrue() {
        // Given
        Alarm alarm = createTestAlarm();
        alarm.setAttackLevel(4); // 设置为高威胁等级

        // When
        boolean result = alarm.isHighThreat();

        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("检查是否为高威胁等级 - 低威胁等级")
    void isHighThreat_whenThreatLevelIsLow_thenReturnFalse() {
        // Given
        Alarm alarm = createTestAlarm();
        alarm.setAttackLevel(2); // 设置为低威胁等级

        // When
        boolean result = alarm.isHighThreat();

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("检查是否可以处理")
    void canBeProcessed_whenUnprocessed_thenReturnTrue() {
        // Given
        Alarm alarm = createTestAlarm();

        // When
        boolean result = alarm.canBeProcessed();

        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("检查是否可以处理 - 已完成状态")
    void canBeProcessed_whenCompleted_thenReturnFalse() {
        // Given
        Alarm alarm = createTestAlarm();
        alarm.updateStatus(AlarmStatusEnum.PROCESSED, 1002);

        // When
        boolean result = alarm.canBeProcessed();

        // Then
        assertFalse(result);
    }

    // ==================== 辅助方法 ====================

    private Alarm createTestAlarm() {
        return Alarm.create(
            "测试告警",
            "TEST_ATTACK",
            3,
            new Date(),
            1001
        );
    }
}
