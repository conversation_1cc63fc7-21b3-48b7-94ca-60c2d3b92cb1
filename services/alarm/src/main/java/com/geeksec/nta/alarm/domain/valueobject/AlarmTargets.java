package com.geeksec.nta.alarm.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 告警目标值对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlarmTargets {
    
    /**
     * 目标类型（如：IP、域名、URL等）
     */
    private String targetType;
    
    /**
     * 目标值
     */
    private String targetValue;
    
    /**
     * 目标端口
     */
    private Integer port;
    
    /**
     * 目标协议
     */
    private String protocol;
    
    /**
     * 目标服务
     */
    private String service;
    
    /**
     * 目标描述
     */
    private String description;
    
    /**
     * 创建IP类型目标
     * 
     * @param ip IP地址
     * @return 目标对象
     */
    public static AlarmTargets ofIp(String ip) {
        Objects.requireNonNull(ip, "目标IP不能为空");
        AlarmTargets target = new AlarmTargets();
        target.setTargetType("IP");
        target.setTargetValue(ip);
        return target;
    }
    
    /**
     * 创建IP+端口类型目标
     * 
     * @param ip IP地址
     * @param port 端口
     * @return 目标对象
     */
    public static AlarmTargets ofIpPort(String ip, Integer port) {
        Objects.requireNonNull(ip, "目标IP不能为空");
        AlarmTargets target = new AlarmTargets();
        target.setTargetType("IP");
        target.setTargetValue(ip);
        target.setPort(port);
        return target;
    }
    
    /**
     * 创建域名类型目标
     * 
     * @param domain 域名
     * @return 目标对象
     */
    public static AlarmTargets ofDomain(String domain) {
        Objects.requireNonNull(domain, "目标域名不能为空");
        AlarmTargets target = new AlarmTargets();
        target.setTargetType("DOMAIN");
        target.setTargetValue(domain);
        return target;
    }
    
    /**
     * 创建URL类型目标
     * 
     * @param url URL地址
     * @return 目标对象
     */
    public static AlarmTargets ofUrl(String url) {
        Objects.requireNonNull(url, "目标URL不能为空");
        AlarmTargets target = new AlarmTargets();
        target.setTargetType("URL");
        target.setTargetValue(url);
        return target;
    }
    
    /**
     * 获取目标的完整地址
     * 
     * @return 完整地址
     */
    public String getFullAddress() {
        StringBuilder address = new StringBuilder(targetValue);
        
        if (port != null && port > 0) {
            address.append(":").append(port);
        }
        
        if (protocol != null && !protocol.trim().isEmpty()) {
            address.insert(0, protocol.toLowerCase() + "://");
        }
        
        return address.toString();
    }
    
    /**
     * 是否为网络服务目标
     * 
     * @return 是否为网络服务目标
     */
    public boolean isNetworkService() {
        return port != null && port > 0 && 
               (protocol != null && !protocol.trim().isEmpty());
    }
    
    /**
     * 是否为Web服务目标
     * 
     * @return 是否为Web服务目标
     */
    public boolean isWebService() {
        if (protocol == null) {
            return false;
        }
        
        String proto = protocol.toLowerCase();
        return "http".equals(proto) || "https".equals(proto) ||
               (port != null && (port == 80 || port == 443 || port == 8080 || port == 8443));
    }
    
    /**
     * 获取目标的显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        if (description != null && !description.trim().isEmpty()) {
            return description;
        }
        
        return getFullAddress();
    }
}
